// 云函数 getSupplyListWithDistance   渲染供应页面
const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();
const _ = db.command;
const $ = db.command.aggregate;

// 计算两点之间的距离（使用Haversine公式）
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // 地球半径，单位：公里
  
  // 将角度转换为弧度
  const deg2rad = (deg) => deg * (Math.PI/180);
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  
  // Haversine公式
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // 距离，单位：公里
  
  // console.log(`计算距离: (${lat1.toFixed(6)}, ${lon1.toFixed(6)}) 到 (${lat2.toFixed(6)}, ${lon2.toFixed(6)}) = ${distance.toFixed(3)}公里`);
  return distance;
}

// 格式化距离显示
function formatDistance(distance) {
  if (distance === null || distance === undefined || isNaN(distance)) {
    return null;
  }
  
  if (distance < 1) {
    // 小于1公里，显示为米
    return `${Math.round(distance * 1000)}米`;
  } else if (distance < 10) {
    // 小于10公里，显示一位小数
    return `${distance.toFixed(1)}公里`;
  } else {
    // 大于等于10公里，显示整数
    return `${Math.round(distance)}公里`;
  }
}

// 格式化时间为多久之前
function formatTimeAgo(dateObj) {
  if (!dateObj) return '';
  
  // 如果是字符串，转换为Date对象
  const date = typeof dateObj === 'string' ? new Date(dateObj) : dateObj;
  const now = new Date();
  
  const diff = Math.floor((now - date) / 1000); // 差异，单位：秒
  
  if (diff < 60) {
    return '刚刚';
  } else if (diff < 3600) {
    return Math.floor(diff / 60) + '分钟前';
  } else if (diff < 86400) {
    return Math.floor(diff / 3600) + '小时前';
  } else if (diff < 2592000) {
    return Math.floor(diff / 86400) + '天前';
  } else if (diff < 31536000) {
    return Math.floor(diff / 2592000) + '个月前';
  } else {
    return Math.floor(diff / 31536000) + '年前';
  }
}

/**
 * 构建查询条件
 */
function buildQuery(tabActive, tabs, searchValue, activeFilters, quickFilters = {}, sortType = 'latest', userProvince = null, userLocation = null, maxDistance = null, existingIds = []) {
  let query = {};
  
  // 添加状态过滤条件，只返回status为"active"的供应项
  query.status = "active";
  
  // 如果有指定ID列表，优先添加到查询条件中
  if (existingIds && existingIds.length > 0) {
    query = _.and([
      { _id: _.in(existingIds) },
      { status: "active" }
    ]);
    // 直接返回ID查询，跳过其他条件
    return query;
  }
  
  // 添加退林还耕筛选条件
  if (quickFilters.isForestReturnFilter) {
    query.return = true;
  }
  
  // 根据分类标签筛选
  if (tabActive > 0) {
    const category = tabs[tabActive];
    query.category = category;
  }
  
  // 根据筛选面板的分类选择筛选
  if (activeFilters.category && activeFilters.category !== 'all') {
    query.category = activeFilters.category;
  }
  
  // 如果是"本省"筛选模式且有用户省份信息，添加省份筛选条件
  if (sortType === 'province' && userProvince) {
    // 如果已经有其他查询条件，使用 _.and 组合
    if (Object.keys(query).length > 0) {
      query = _.and([
        query,
        { province: userProvince }
      ]);
    } else {
      // 没有其他查询条件，直接设置省份条件
      query.province = userProvince;
    }
  }
  
  // 如果有搜索关键词
  if (searchValue) {
    // 使用正则表达式进行模糊搜索，只搜索标题字段
    const keyword = searchValue;
    const keywordQuery = {
      title: db.RegExp({
        regexp: keyword,
        options: 'i'
      })
    };
    
    // 合并分类查询和关键词查询
    if (tabActive > 0) {
      // 已有分类查询，需要同时满足分类和关键词条件
      query = _.and([
        { category: tabs[tabActive] },
        keywordQuery,
        { status: "active" } // 确保status条件被保留
      ]);
    } else {
      // 没有分类查询，只有关键词查询
      query = _.and([
        keywordQuery,
        { status: "active" } // 确保status条件被保留
      ]);
    }
  }
  
  // 添加筛选条件
  let filterConditions = [];

  // 处理quick-filter-bar的筛选条件
  // 使用±20公分范围筛选（除了丛生杆数）
  // 高度筛选
  if (quickFilters.exactHeight !== undefined && quickFilters.exactHeight !== '') {
    const exactHeight = Number(quickFilters.exactHeight);
    filterConditions.push({
      height: _.and([
        _.gte(exactHeight - 20),
        _.lte(exactHeight + 20)
      ])
    });
  }

  // 树经筛选（包括米径、地径、胸径）
  if (quickFilters.exactTreeDiameter !== undefined && quickFilters.exactTreeDiameter !== '') {
    const exactTreeDiameter = Number(quickFilters.exactTreeDiameter);
    
    // 根据高度值确定树经误差范围
    let treeDiameterError = 20; // 默认误差值为±20
    
    // 根据高度精确值调整树经误差范围
    if (quickFilters.exactHeight !== undefined && quickFilters.exactHeight !== '') {
      const exactHeight = Number(quickFilters.exactHeight);
      if (exactHeight <= 10) {
        treeDiameterError = 2; // 高度<=10，树经误差为±2
      } else if (exactHeight <= 20) {
        treeDiameterError = 3; // 高度(10,20]，树经误差为±3
      } else {
        treeDiameterError = 10; // 高度>20，树经误差为±10
      }
    }
    
    // 创建OR条件，任一径符合条件即可
    filterConditions.push(_.or([
      // 米径符合条件
      {
        meter_diameter: _.and([
          _.gte(exactTreeDiameter - treeDiameterError),
          _.lte(exactTreeDiameter + treeDiameterError)
        ])
      },
      // 地径符合条件
      {
        ground_diameter: _.and([
          _.gte(exactTreeDiameter - treeDiameterError),
          _.lte(exactTreeDiameter + treeDiameterError)
        ])
      },
      // 胸径符合条件
      {
        thorax_diameter: _.and([
          _.gte(exactTreeDiameter - treeDiameterError),
          _.lte(exactTreeDiameter + treeDiameterError)
        ])
      }
    ]));
  }

  // 冠幅筛选
  if (quickFilters.exactCanopy !== undefined && quickFilters.exactCanopy !== '') {
    const exactCanopy = Number(quickFilters.exactCanopy);
    filterConditions.push({
      canopy: _.and([
        _.gte(exactCanopy - 20),
        _.lte(exactCanopy + 20)
      ])
    });
  }

  // 丛生杆数筛选 - 精确匹配
  if (quickFilters.exactClumpCount !== undefined && quickFilters.exactClumpCount !== '') {
    filterConditions.push({ clumpCount: Number(quickFilters.exactClumpCount) });
  }

  // 杆径筛选 - 允许±5厘米误差
  if (quickFilters.exactClumpDiameter !== undefined && quickFilters.exactClumpDiameter !== '') {
    const exactClumpDiameter = Number(quickFilters.exactClumpDiameter);
    filterConditions.push({
      clumpDiameter: _.and([
        _.gte(exactClumpDiameter - 5),
        _.lte(exactClumpDiameter + 5)
      ])
    });
  }

  // 分支筛选 - 精确匹配
  if (quickFilters.exactBranchPos !== undefined && quickFilters.exactBranchPos !== '') {
    filterConditions.push({
      branchPos: Number(quickFilters.exactBranchPos)
    });
  }
  
  // 添加常规筛选条件处理
  // 价格筛选 - 同时考虑上车价(price)和地价(land_price)
  if (activeFilters.isPriceExact && activeFilters.exactPrice !== '') {
    // 精确价格筛选 - 上车价或地价任一满足条件即可
    const exactPrice = Number(activeFilters.exactPrice);
    filterConditions.push(_.or([
      { price: exactPrice },
      { land_price: exactPrice }
    ]));
  } else {
    // 区间价格筛选
    if (activeFilters.minPrice !== undefined && activeFilters.minPrice !== '') {
      const minPrice = Number(activeFilters.minPrice);
      filterConditions.push(_.or([
        { price: _.gte(minPrice) },
        { land_price: _.gte(minPrice) }
      ]));
    }
    if (activeFilters.maxPrice !== undefined && activeFilters.maxPrice !== '') {
      const maxPrice = Number(activeFilters.maxPrice);
      filterConditions.push(_.or([
        { price: _.lte(maxPrice) },
        { land_price: _.lte(maxPrice) }
      ]));
    }
  }
  
  // 高度筛选 - 如果quick-filter没有设置
  if (!quickFilters.exactHeight && activeFilters.isHeightExact && activeFilters.exactHeight !== '') {
    // 精确高度筛选
    filterConditions.push({ height: Number(activeFilters.exactHeight) });
  } else if (!quickFilters.exactHeight) {
    // 区间高度筛选
    if (activeFilters.minHeight !== undefined && activeFilters.minHeight !== '') {
      filterConditions.push({ height: _.gte(Number(activeFilters.minHeight)) });
    }
    if (activeFilters.maxHeight !== undefined && activeFilters.maxHeight !== '') {
      filterConditions.push({ height: _.lte(Number(activeFilters.maxHeight)) });
    }
  }
  
  // 冠幅筛选 - 如果quick-filter没有设置
  if (!quickFilters.exactCanopy && activeFilters.isCanopyExact && activeFilters.exactCanopy !== '') {
    // 精确冠幅筛选
    filterConditions.push({ canopy: Number(activeFilters.exactCanopy) });
  } else if (!quickFilters.exactCanopy) {
    // 区间冠幅筛选
    if (activeFilters.minCanopy !== undefined && activeFilters.minCanopy !== '') {
      filterConditions.push({ canopy: _.gte(Number(activeFilters.minCanopy)) });
    }
    if (activeFilters.maxCanopy !== undefined && activeFilters.maxCanopy !== '') {
      filterConditions.push({ canopy: _.lte(Number(activeFilters.maxCanopy)) });
    }
  }
  
  // 树经筛选 - 如果quick-filter没有设置
  if (!quickFilters.exactTreeDiameter && activeFilters.isTreeDiameterExact && activeFilters.exactTreeDiameter !== '') {
    // 精确树经筛选（包括米径、地径、胸径）
    const exactTreeDiameter = Number(activeFilters.exactTreeDiameter);
    
    // 根据高度值确定树经误差范围
    let treeDiameterError = 0; // 默认精确匹配
    
    // 根据高度精确值调整树经误差范围
    if (activeFilters.isHeightExact && activeFilters.exactHeight !== '') {
      const exactHeight = Number(activeFilters.exactHeight);
      if (exactHeight <= 10) {
        treeDiameterError = 2; // 高度<=10，树经误差为±2
      } else if (exactHeight <= 20) {
        treeDiameterError = 3; // 高度(10,20]，树经误差为±3
      } else {
        treeDiameterError = 10; // 高度>20，树经误差为±10
      }
    }
    
    // 如果有误差范围，使用范围查询，否则使用精确查询
    if (treeDiameterError > 0) {
      filterConditions.push(_.or([
        {
          meter_diameter: _.and([
            _.gte(exactTreeDiameter - treeDiameterError),
            _.lte(exactTreeDiameter + treeDiameterError)
          ])
        },
        {
          ground_diameter: _.and([
            _.gte(exactTreeDiameter - treeDiameterError),
            _.lte(exactTreeDiameter + treeDiameterError)
          ])
        },
        {
          thorax_diameter: _.and([
            _.gte(exactTreeDiameter - treeDiameterError),
            _.lte(exactTreeDiameter + treeDiameterError)
          ])
        }
      ]));
    } else {
      // 原来的精确匹配
      filterConditions.push(_.or([
        { meter_diameter: exactTreeDiameter },
        { ground_diameter: exactTreeDiameter },
        { thorax_diameter: exactTreeDiameter }
      ]));
    }
  } else if (!quickFilters.exactTreeDiameter) {
    // 区间树经筛选
    if (activeFilters.minTreeDiameter !== undefined && activeFilters.minTreeDiameter !== '') {
      const minTreeDiameter = Number(activeFilters.minTreeDiameter);
      filterConditions.push(_.or([
        { meter_diameter: _.gte(minTreeDiameter) },
        { ground_diameter: _.gte(minTreeDiameter) },
        { thorax_diameter: _.gte(minTreeDiameter) }
      ]));
    }
    if (activeFilters.maxTreeDiameter !== undefined && activeFilters.maxTreeDiameter !== '') {
      const maxTreeDiameter = Number(activeFilters.maxTreeDiameter);
      filterConditions.push(_.or([
        { meter_diameter: _.lte(maxTreeDiameter) },
        { ground_diameter: _.lte(maxTreeDiameter) },
        { thorax_diameter: _.lte(maxTreeDiameter) }
      ]));
    }
  }
  
  // 杯口筛选
  if (activeFilters.isCupExact && activeFilters.exactCup !== '') {
    // 精确杯口筛选
    filterConditions.push({ cup: Number(activeFilters.exactCup) });
  } else {
    // 区间杯口筛选
    if (activeFilters.minCup !== undefined && activeFilters.minCup !== '') {
      filterConditions.push({ cup: _.gte(Number(activeFilters.minCup)) });
    }
    if (activeFilters.maxCup !== undefined && activeFilters.maxCup !== '') {
      filterConditions.push({ cup: _.lte(Number(activeFilters.maxCup)) });
    }
  }
  
  // 胸径筛选
  if (activeFilters.isChestDiameterExact && activeFilters.exactChestDiameter !== '') {
    // 精确胸径筛选
    filterConditions.push({ thorax_diameter: Number(activeFilters.exactChestDiameter) });
  } else {
    // 区间胸径筛选
    if (activeFilters.minChestDiameter !== undefined && activeFilters.minChestDiameter !== '') {
      filterConditions.push({ thorax_diameter: _.gte(Number(activeFilters.minChestDiameter)) });
    }
    if (activeFilters.maxChestDiameter !== undefined && activeFilters.maxChestDiameter !== '') {
      filterConditions.push({ thorax_diameter: _.lte(Number(activeFilters.maxChestDiameter)) });
    }
  }
  
  // 地径筛选
  if (activeFilters.isGroundDiameterExact && activeFilters.exactGroundDiameter !== '') {
    // 精确地径筛选
    filterConditions.push({ ground_diameter: Number(activeFilters.exactGroundDiameter) });
  } else {
    // 区间地径筛选
    if (activeFilters.minGroundDiameter !== undefined && activeFilters.minGroundDiameter !== '') {
      filterConditions.push({ ground_diameter: _.gte(Number(activeFilters.minGroundDiameter)) });
    }
    if (activeFilters.maxGroundDiameter !== undefined && activeFilters.maxGroundDiameter !== '') {
      filterConditions.push({ ground_diameter: _.lte(Number(activeFilters.maxGroundDiameter)) });
    }
  }
  
  // 分支点筛选 - 如果quick-filter没有设置
  if (!quickFilters.exactBranchPos && activeFilters.isBranchPosExact && activeFilters.exactBranchPos !== '') {
    // 精确分支点筛选
    filterConditions.push({ branchPos: Number(activeFilters.exactBranchPos) });
  } else if (!quickFilters.exactBranchPos) {
    // 区间分支点筛选
    if (activeFilters.minBranchPos !== undefined && activeFilters.minBranchPos !== '') {
      filterConditions.push({ branchPos: _.gte(Number(activeFilters.minBranchPos)) });
    }
    if (activeFilters.maxBranchPos !== undefined && activeFilters.maxBranchPos !== '') {
      filterConditions.push({ branchPos: _.lte(Number(activeFilters.maxBranchPos)) });
    }
  }
  
  // 丛生数量筛选 - 如果quick-filter没有设置
  if (!quickFilters.exactClumpCount && activeFilters.isClumpCountExact && activeFilters.exactClumpCount !== '') {
    // 精确丛生数量筛选
    filterConditions.push({ clumpCount: Number(activeFilters.exactClumpCount) });
  } else if (!quickFilters.exactClumpCount) {
    // 区间丛生数量筛选
    if (activeFilters.minClumpCount !== undefined && activeFilters.minClumpCount !== '') {
      filterConditions.push({ clumpCount: _.gte(Number(activeFilters.minClumpCount)) });
    }
    if (activeFilters.maxClumpCount !== undefined && activeFilters.maxClumpCount !== '') {
      filterConditions.push({ clumpCount: _.lte(Number(activeFilters.maxClumpCount)) });
    }
  }
  
  // 杆径筛选 - 如果quick-filter没有设置
  if (!quickFilters.exactClumpDiameter && activeFilters.isClumpDiameterExact && activeFilters.exactClumpDiameter !== '') {
    // 精确杆径筛选
    filterConditions.push({ clumpDiameter: Number(activeFilters.exactClumpDiameter) });
  } else if (!quickFilters.exactClumpDiameter) {
    // 区间杆径筛选
    if (activeFilters.minClumpDiameter !== undefined && activeFilters.minClumpDiameter !== '') {
      filterConditions.push({ clumpDiameter: _.gte(Number(activeFilters.minClumpDiameter)) });
    }
    if (activeFilters.maxClumpDiameter !== undefined && activeFilters.maxClumpDiameter !== '') {
      filterConditions.push({ clumpDiameter: _.lte(Number(activeFilters.maxClumpDiameter)) });
    }
  }
  
  // 质量筛选
  if (activeFilters.quality !== undefined && activeFilters.quality !== 'all') {
    filterConditions.push({ quality: activeFilters.quality });
  }
  
  // 如果有位置筛选（距离筛选）, 在这里不添加条件
  // 位置筛选将在后续使用geo.near直接进行
  
  // 如果有筛选条件，使用 _.and 组合
  if (filterConditions.length > 0) {
    if (Object.keys(query).length > 0) {
      query = _.and([query, ...filterConditions]);
    } else {
      if (filterConditions.length === 1) {
        // 确保即使只有一个筛选条件，也包含status条件
        query = _.and([filterConditions[0], { status: "active" }]);
      } else {
        // 添加status条件到筛选条件列表中
        filterConditions.push({ status: "active" });
        query = _.and(filterConditions);
      }
    }
  }
  
  return query;
}

/**
 * 合并新旧图片列表
 */
function mergeImageLists(imageList, newImageList) {
  const mergedImages = [];

  // 处理新图片列表（包含拍照时间）
  if (newImageList && Array.isArray(newImageList) && newImageList.length > 0) {
    // 按拍照时间排序（最新的在前）
    const sortedNewImages = newImageList
      .filter(item => item && item.url) // 过滤无效数据
      .sort((a, b) => {
        const timeA = new Date(a.captureTime || 0);
        const timeB = new Date(b.captureTime || 0);
        return timeB - timeA; // 降序排列，最新的在前
      })
      .map(item => item.url); // 提取URL

    mergedImages.push(...sortedNewImages);
  }

  // 处理老图片列表
  if (imageList && Array.isArray(imageList) && imageList.length > 0) {
    // 按数组顺序添加老图片
    mergedImages.push(...imageList);
  }

  return mergedImages;
}

// 主函数
exports.main = async (event, context) => {
  // console.log('==== 开始处理供应列表请求 ====');
  // console.log('请求参数:', JSON.stringify(event.params));
  
  const { 
    page = 1,
    pageSize = 10,
    tabActive = 0,
    tabs = ['全部', '乔木', '灌木', '藤本类', '草皮类', '花草', '种子'],
    searchValue = '',
    sortType = 'latest',
    activeFilters = {},
    userLocation = null,
    quickFilters = {}, // 接收quick-filter-bar的筛选条件
    maxDistance = null, // 接收最大距离筛选
    userProvince = null, // 接收用户省份信息
    existingIds = [] // 接收已有数据的ID列表，用于避免返回重复数据
  } = event.params || event;
  
  // console.log('处理关键参数:', {
  //   page,
  //   pageSize,
  //   tabActive,
  //   hasUserLocation: userLocation ? true : false,
  //   sortType,
  //   maxDistance,
  //   userProvince,
  //   existingIds: existingIds && existingIds.length > 0 ? existingIds.length : '无'
  // });
  
  // if (userLocation) {
  //   console.log('用户位置信息:', JSON.stringify(userLocation));
  // } else {
  //   console.log('未提供用户位置信息');
  // }
  
  try {
    // 计算跳过的数量
    const skip = (page - 1) * pageSize;
    
    // 构建基本查询条件（不包括距离筛选）
    let query = buildQuery(tabActive, tabs, searchValue, activeFilters, quickFilters, sortType, userProvince, userLocation, maxDistance, existingIds);
    
    // 设置默认排序方式为photoUpdated而不是createTime
    let orderField = 'photoUpdated';
    let orderDirection = 'desc';
    
    // 如果是距离筛选模式且有用户位置信息，使用地理位置索引查询
    if ((sortType === 'distance5' || sortType === 'distance50' || sortType === 'distance500') && userLocation) {
      // console.log('使用地理位置索引进行距离筛选');
      
      // 确定距离限制（单位：米）
      const distanceLimit = maxDistance ? maxDistance * 1000 : 
                         (sortType === 'distance5' ? 5000 : 
                          sortType === 'distance50' ? 50000 : 500000);
      
      // console.log(`距离筛选模式: ${sortType}, 限制距离: ${distanceLimit/1000}公里`);
      
      // 创建地理点 - 使用正确的API
      let userPoint;
      if (userLocation.coordinates && Array.isArray(userLocation.coordinates) && userLocation.coordinates.length >= 2) {
        // 如果已经是坐标数组，直接使用
        userPoint = db.Geo.Point(userLocation.coordinates[0], userLocation.coordinates[1]);
        // console.log(`使用coordinates创建地理点: [${userLocation.coordinates[0]}, ${userLocation.coordinates[1]}]`);
      } else if (userLocation.longitude !== undefined && userLocation.latitude !== undefined) {
        // 如果是{longitude, latitude}格式，转换为地理点
        userPoint = db.Geo.Point(userLocation.longitude, userLocation.latitude);
        // console.log(`使用longitude/latitude创建地理点: [${userLocation.longitude}, ${userLocation.latitude}]`);
      } else {
        throw new Error('无效的用户位置信息');
      }
      
      // 构建地理位置查询 - 使用正确的API
      // 如果已经有查询条件，使用_.and组合
      if (Object.keys(query).length > 0) {
        query = _.and([
          query,
          {
            location: _.geoNear({
              geometry: userPoint,
              maxDistance: distanceLimit, // 单位：米
              minDistance: 0
            })
          }
        ]);
      } else {
        // 没有其他查询条件，直接使用地理位置查询和状态条件
        query = _.and([
          {
            location: _.geoNear({
          geometry: userPoint,
          maxDistance: distanceLimit, // 单位：米
          minDistance: 0
            })
          },
          { status: "active" }
        ]);
      }
      
      // console.log('应用地理位置查询:', JSON.stringify(query));
      
      // 对于地理位置near查询，结果已经按距离排序，不需要额外排序
      // 但为了保持与其他模式一致，我们仍使用photoUpdated时间排序作为第二排序条件
    }
    
    // console.log('查询条件:', JSON.stringify(query));
    
    // 解决地理位置查询分页问题
    // 对于地理位置查询，count()方法可能不准确，我们采用多查询一条数据来判断是否有下一页
    const extraLimit = pageSize + 1;
    
    // 创建一个查询构建器
    let queryBuilder = db.collection('supply_content').where(query);
    
    // 处理没有photoUpdated字段的排序问题
    if (orderField === 'photoUpdated') {
      // 先按照photoUpdated是否存在排序，然后按照photoUpdated降序排序
      queryBuilder = queryBuilder.orderBy('photoUpdated', 'desc');
    } else {
      // 其他排序字段
      queryBuilder = queryBuilder.orderBy(orderField, orderDirection);
    }
    
    // 应用分页，多获取一条数据用于判断是否有下一页
    const queryResult = await queryBuilder.skip(skip).limit(extraLimit).get();
    
    const rawData = queryResult.data;
    // console.log(`查询到 ${rawData.length} 条数据`);
    
    // 判断是否有更多数据
    const hasMore = rawData.length > pageSize;
    
    // 如果多获取了一条数据，需要去掉最后一条
    const resultData = hasMore ? rawData.slice(0, pageSize) : rawData;
    
    // 处理返回数据
    let processedData = resultData.map(item => {
      // 计算时间 - 优先使用photoUpdated字段
      const timeDisplayDate = item.photoUpdated || item.createTime;
      const timeAgo = formatTimeAgo(timeDisplayDate);
      
      // 格式化距离显示
      let calculatedDistance = null;
      let distanceValue = null;
      
      // 如果是地理位置查询结果，会自动包含距离信息
      if (userLocation && item.location) {
        try {
          // 获取用户位置坐标
          let userLat, userLon;
          if (userLocation.coordinates && Array.isArray(userLocation.coordinates) && userLocation.coordinates.length >= 2) {
            userLon = userLocation.coordinates[0];
            userLat = userLocation.coordinates[1];
          } else if (userLocation.latitude !== undefined && userLocation.longitude !== undefined) {
            userLat = userLocation.latitude;
            userLon = userLocation.longitude;
          }
          
          // 获取供应项位置坐标
          let itemLat, itemLon;
          if (item.location && item.location.coordinates && Array.isArray(item.location.coordinates) && item.location.coordinates.length >= 2) {
            itemLon = item.location.coordinates[0];
            itemLat = item.location.coordinates[1];
          } else if (item.location && item.location.latitude !== undefined && item.location.longitude !== undefined) {
            itemLat = item.location.latitude;
            itemLon = item.location.longitude;
          }
          
          // 如果有有效坐标，计算距离
          if (userLat && userLon && itemLat && itemLon) {
            distanceValue = calculateDistance(userLat, userLon, itemLat, itemLon);
            calculatedDistance = "距离你" + formatDistance(distanceValue);
            // 记录距离计算结果
            console.log(`ID:${item._id} 距离计算: ${calculatedDistance} (${distanceValue.toFixed(2)}公里)`);
          } else {
            console.log(`ID:${item._id} 坐标不完整，无法计算距离. userLat:${userLat}, userLon:${userLon}, itemLat:${itemLat}, itemLon:${itemLon}`);
          }
        } catch (error) {
          console.error(`计算距离出错:`, error);
        }
      } else {
        if (!userLocation) {
          console.log(`没有用户位置信息，无法计算距离`);
        } else if (!item.location) {
          console.log(`ID:${item._id} 没有供应项位置信息，无法计算距离`);
        }
      }
      
      // 合并新旧图片列表
      const mergedImages = mergeImageLists(item.imageList, item.newImageList);

      // 返回处理后的数据 - 恢复原来的字段结构，确保前端渲染正常
      return {
        _id: item._id,
        id: item._id,
        titlePrefix: '【供应】',
        titleContent: item.title || '',
        title: `【供应】${item.title || ''}`,
        content: item.content || '',
        price: item.price || 0,
        category: item.category || '其他',
        images: mergedImages,
        publishTime: item.photoUpdated || item.createTime, // 使用photoUpdated作为发布时间
        timeAgo: timeAgo,
        location: item.location,
        calculatedDistance: calculatedDistance,
        distanceValue: distanceValue,
        rawData: item // 保存原始数据以便后续使用
      };
    });
    
    // console.log(`最终返回数据: ${processedData.length}条，是否有更多: ${hasMore}`);
    
    // 返回结果
    return {
      code: 0,
      msg: '获取数据成功',
      data: processedData,
      page: page,
      pageSize: pageSize,
      hasMore: hasMore,
      sortType: sortType
    };
  } catch (error) {
    console.error('获取供应列表失败:', error);
    return {
      code: -1,
      msg: '获取数据失败',
      error: error.message
    };
  }
}; 