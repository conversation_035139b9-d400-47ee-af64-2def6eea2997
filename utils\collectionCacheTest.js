// 收藏缓存测试工具
// 用于验证缓存大小限制是否生效

class CollectionCacheTest {
  /**
   * 模拟大量单个查询，测试缓存限制
   */
  static testSingleCacheLimit() {
    console.log('=== 单个查询缓存限制测试 ===')
    
    const collectionUtils = require('./collectionUtils.js')
    
    // 模拟添加600个缓存项（超过500的限制）
    for (let i = 1; i <= 600; i++) {
      const cacheKey = `supply_test_${i}`
      collectionUtils.setSingleCache(cacheKey, {
        isCollected: i % 2 === 0 // 偶数为已收藏
      })
      
      // 每100个输出一次状态
      if (i % 100 === 0) {
        const status = collectionUtils.getCacheStatus()
        console.log(`添加了${i}个项目，当前单个缓存大小: ${status.singleCache.size}，使用率: ${status.singleCache.usage}`)
      }
    }
    
    // 最终状态
    const finalStatus = collectionUtils.getCacheStatus()
    console.log('最终缓存状态:', finalStatus)
    
    // 验证缓存大小是否被限制在500以内
    if (finalStatus.singleCache.size <= 500) {
      console.log('✅ 缓存大小限制生效，最大缓存数量:', finalStatus.singleCache.size)
    } else {
      console.log('❌ 缓存大小限制失效，当前缓存数量:', finalStatus.singleCache.size)
    }
  }

  /**
   * 测试缓存清理功能
   */
  static testCacheCleanup() {
    console.log('\n=== 缓存清理功能测试 ===')
    
    const collectionUtils = require('./collectionUtils.js')
    
    // 添加一些测试缓存
    for (let i = 1; i <= 10; i++) {
      const cacheKey = `demand_test_${i}`
      collectionUtils.setSingleCache(cacheKey, {
        isCollected: true
      })
    }
    
    console.log('添加了10个测试缓存项')
    
    // 手动触发清理
    collectionUtils.cleanupExpiredSingleCache()
    
    const status = collectionUtils.getCacheStatus()
    console.log('清理后缓存状态:', status)
  }

  /**
   * 模拟用户浏览行为
   */
  static simulateUserBrowsing() {
    console.log('\n=== 模拟用户浏览行为 ===')
    
    const collectionUtils = require('./collectionUtils.js')
    
    // 模拟用户浏览100个不同的详情页面
    console.log('模拟用户浏览100个详情页面...')
    
    for (let i = 1; i <= 100; i++) {
      const itemType = i % 2 === 0 ? 'supply' : 'demand'
      const cacheKey = `${itemType}_user_browse_${i}`
      
      collectionUtils.setSingleCache(cacheKey, {
        isCollected: Math.random() > 0.7 // 30%的概率已收藏
      })
    }
    
    const status = collectionUtils.getCacheStatus()
    console.log('浏览100个页面后的缓存状态:', status)
    
    // 计算内存占用估算
    const estimatedMemory = status.singleCache.size * 100 // 每个缓存项约100字节
    console.log(`估算内存占用: ${estimatedMemory} 字节 (${(estimatedMemory / 1024).toFixed(2)} KB)`)
  }

  /**
   * 运行所有测试
   */
  static runAllTests() {
    this.testSingleCacheLimit()
    this.testCacheCleanup()
    this.simulateUserBrowsing()
    
    console.log('\n=== 测试总结 ===')
    console.log('✅ 单个查询缓存已添加大小限制 (最大500个)')
    console.log('✅ 自动清理机制已启用 (每30秒清理过期项)')
    console.log('✅ 缓存状态监控功能已添加')
    console.log('✅ FIFO清理策略已实现')
  }
}

module.exports = CollectionCacheTest

// 如果直接运行此文件，执行测试
if (require.main === module) {
  CollectionCacheTest.runAllTests()
}
