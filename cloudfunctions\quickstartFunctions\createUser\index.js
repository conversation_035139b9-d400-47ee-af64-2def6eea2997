// 云函数：createUser
// 用于创建用户记录
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('创建用户云函数调用，参数:', event);
  
  // 获取微信上下文
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  // 获取数据库引用
  const db = cloud.database();
  
  try {
    // 检查用户数据是否有效
    const userData = event.userData;
    if (!userData || !userData.nickName) {
      console.error('用户数据无效');
      return { 
        success: false, 
        errMsg: '用户数据无效'
      };
    }
    
    // 检查该openid是否已存在用户
    const existingUser = await db.collection('users')
                              .where({ openid: openid })
                              .get();
    
    if (existingUser && existingUser.data && existingUser.data.length > 0) {
      console.log('该用户已存在，将更新用户数据');
      
      const userId = existingUser.data[0]._id;
      
      // 更新用户信息
      await db.collection('users').doc(userId).update({
        data: {
          nickName: userData.nickName,
          avatarUrl: userData.avatarUrl,
          gender: userData.gender,
          country: userData.country,
          province: userData.province,
          city: userData.city,
          language: userData.language,
          lastLoginTime: db.serverDate(),
          isLogined: true
        }
      });
      
      // 返回已存在的用户数据
      const updatedUser = await db.collection('users').doc(userId).get();
      
      return {
        success: true,
        data: updatedUser.data,
        message: '用户已存在，数据已更新'
      };
    }
    
    // 准备创建新用户
    const newUserData = {
      openid: openid, // 使用从云函数上下文获取的openid
      nickName: userData.nickName,
      avatarUrl: userData.avatarUrl,
      gender: userData.gender,
      country: userData.country,
      province: userData.province,
      city: userData.city,
      language: userData.language,
      createTime: db.serverDate(),
      lastLoginTime: db.serverDate(),
      isLogined: true,
      level: 1,
      exp: 0,
      collectionCount: 0,
      cartCount: 0,
      discountCount: 0,
      reward: 0
    };
    
    // 添加到数据库
    const addResult = await db.collection('users').add({
      data: newUserData
    });
    
    if (addResult._id) {
      console.log('创建用户成功，ID:', addResult._id);
      
      // 获取新创建的用户数据
      const newUser = await db.collection('users').doc(addResult._id).get();
      
      return {
        success: true,
        data: newUser.data,
        message: '用户创建成功'
      };
    } else {
      console.error('创建用户失败，无法获取用户ID');
      return {
        success: false,
        errMsg: '创建用户失败，无法获取用户ID'
      };
    }
  } catch (err) {
    console.error('创建用户云函数执行失败:', err);
    return {
      success: false,
      errMsg: err.message || '创建用户失败'
    };
  }
}; 