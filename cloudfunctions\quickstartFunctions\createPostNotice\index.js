// 创建帖子发布通知 
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 主函数 - 创建帖子发布通知
exports.main = async (event, context) => {
  try {
    // 解析参数
    const { 
      postId,       // 帖子ID
      postType,     // 帖子类型: 'supply' 或 'demand'
      publisherId,  // 发布者ID
      publisherName,// 发布者昵称
      plantName,    // 植物名称
      postTitle,    // 帖子标题
      postContent   // 帖子内容
    } = event
    
    // 验证必要参数
    if (!postId || !publisherId || !publisherName || !plantName) {
      return {
        success: false,
        errMsg: '参数不完整，需要提供postId、publisherId、publisherName和plantName'
      }
    }
    
    // 构建通知文本
    let noticeText = ''
    if (postType === 'supply') {
      noticeText = `${publisherName} 发布了一条 ${plantName} 供应信息`
    } else if (postType === 'demand') {
      noticeText = `${publisherName} 发布了一条 ${plantName} 求购信息`
    } else {
      noticeText = `${publisherName} 发布了一条关于 ${plantName} 的信息`
    }
    
    // 创建通知数据
    const noticeData = {
      postId: postId,                 // 关联帖子ID
      postType: postType || 'supply', // 帖子类型，默认为供应
      publisherId: publisherId,       // 发布者ID
      publisherName: publisherName,   // 发布者昵称
      plantName: plantName,           // 植物名称
      noticeText: noticeText,         // 通知文本
      postTitle: postTitle || '',     // 帖子标题
      postContent: postContent || '', // 帖子内容
      createTime: db.serverDate(),    // 创建时间，使用服务器时间
      isPublic: true,                 // 是否公开展示
      isRead: false,                  // 是否已读
      isDeleted: false                // 是否已删除
    }
    
    // 插入数据到notice集合
    const result = await db.collection('notice').add({
      data: noticeData
    })
    
    console.log('通知创建成功', result)
    
    // 返回结果
    return {
      success: true,
      noticeId: result._id,
      noticeText: noticeText,
      message: '通知创建成功'
    }
    
  } catch (error) {
    console.error('创建通知失败', error)
    return {
      success: false,
      errMsg: error.message || '创建通知失败'
    }
  }
} 