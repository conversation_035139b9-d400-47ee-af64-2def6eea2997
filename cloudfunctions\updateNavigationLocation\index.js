// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { 
      postId, 
      latitude, 
      longitude, 
      address 
    } = event

    // 参数验证
    if (!postId || !latitude || !longitude) {
      return {
        success: false,
        message: '缺少必要参数'
      }
    }

    // 创建新的GeoPoint
    const newLocation = db.Geo.Point(longitude, latitude)

    // 更新所有该帖子的导航记录的位置信息
    const updateData = {
      location: newLocation,
      updateTime: db.serverDate()
    }

    // 如果有地址信息，也更新地址
    if (address) {
      updateData.address = address
    }

    const result = await db.collection('navigation_records')
      .where({
        postId: postId
      })
      .update({
        data: updateData
      })

    return {
      success: true,
      message: '导航记录位置更新成功',
      updatedCount: result.stats.updated
    }

  } catch (error) {
    console.error('更新导航记录位置失败:', error)
    return {
      success: false,
      message: '更新导航记录位置失败',
      error: error.message
    }
  }
}
