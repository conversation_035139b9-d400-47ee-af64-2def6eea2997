Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    countdown: 50
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      // 当组件初始化时，设置倒计时初始值
      this.setData({ countdown: 50 });
      
      // 当组件显示时，开始倒计时
      if (this.properties.visible) {
        this.startCountdown();
      }
    },
    detached() {
      // 组件销毁时，清除定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    }
  },
  
  /**
   * 属性监听器
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        // 当组件变为可见时，重置倒计时并开始
        this.setData({ countdown: 3 });
        console.log('倒计时开始，初始值：', this.data.countdown);
        this.startCountdown();
      } else {
        // 当组件隐藏时，清除定时器
        if (this.countdownTimer) {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 开始倒计时
    startCountdown() {
      // 清除可能存在的旧定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }
      
      // 设置新的定时器，每秒减少1
      this.countdownTimer = setInterval(() => {
        const newCountdown = this.data.countdown - 1;
        console.log('倒计时更新：', newCountdown);
        
        if (newCountdown <= 0) {
          // 倒计时结束，关闭欢迎页面
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
          this.triggerEvent('close');
        } else {
          // 更新倒计时
          this.setData({ countdown: newCountdown });
        }
      }, 1000);
    },
    
    // 手动关闭欢迎页面
    onClose() {
      // 添加按钮点击效果
      wx.vibrateShort({
        type: 'light'
      });
      
      // 清除定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
      
      // 触发关闭事件
      this.triggerEvent('close');
    }
  }
}) 