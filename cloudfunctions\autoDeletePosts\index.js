// 云函数 - autoDeletePosts
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async () => {
  try {
    console.log('开始执行自动删除通知，基于数量限制逻辑')

    // 1. 获取notice集合的总记录数
    const countResult = await db.collection('notice').count()
    const totalCount = countResult.total

    console.log('notice集合当前总记录数:', totalCount)

    // 2. 检查是否需要删除（总数>=20时才删除）
    if (totalCount < 20) {
      return {
        success: true,
        message: `当前记录数${totalCount}小于20，无需删除`
      }
    }

    // 3. 计算需要删除的数量（总数-20）
    const deleteCount = totalCount - 20
    console.log('需要删除的记录数:', deleteCount)

    // 4. 如果删除数量为0或负数，直接返回
    if (deleteCount <= 0) {
      return {
        success: true,
        message: `当前记录数${totalCount}，无需删除任何记录`
      }
    }

    // 5. 查询最老的记录（按createTime升序排列，取前deleteCount条）
    const postsToDelete = await db.collection('notice')
      .orderBy('createTime', 'asc') // 按创建时间升序排列，最老的在前面
      .limit(deleteCount) // 限制删除数量
      .get()

    console.log('查询到需要删除的最老记录数量:', postsToDelete.data.length)

    // 6. 如果查询到的记录数与计算的不一致，记录警告但继续执行
    if (postsToDelete.data.length !== deleteCount) {
      console.warn(`预期删除${deleteCount}条，实际查询到${postsToDelete.data.length}条`)
    }

    // 7. 如果没有需要删除的记录，直接返回
    if (postsToDelete.data.length === 0) {
      return {
        success: true,
        message: '没有查询到需要删除的记录'
      }
    }

    // 8. 执行批量删除操作
    const deletePromises = postsToDelete.data.map(post => {
      console.log(`准备删除记录: ${post._id}, 创建时间: ${post.createTime}`)
      return db.collection('notice').doc(post._id).remove()
    })

    // 9. 等待所有删除操作完成
    const deleteResults = await Promise.all(deletePromises)

    // 10. 统计删除结果
    const successCount = deleteResults.filter(result => result.stats && result.stats.removed > 0).length

    console.log(`删除操作完成，成功删除${successCount}条记录`)

    return {
      success: true,
      totalCount: totalCount,
      deletedCount: successCount,
      remainingCount: totalCount - successCount,
      message: `成功删除${successCount}条最老的通知记录，剩余${totalCount - successCount}条记录`
    }
  } catch (error) {
    console.error('自动删除通知失败:', error)
    return {
      success: false,
      error: error.message || error,
      message: '自动删除通知过程中发生错误'
    }
  }
}