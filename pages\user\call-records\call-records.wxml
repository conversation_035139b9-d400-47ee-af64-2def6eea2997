<view class="page-container">
  <!-- 导航栏组件 -->
  <view class="nav-container">
    <navigation-bar title="拨号记录" back="{{true}}" homeButton="{{true}}" bind:back="onNavBack" bind:home="onNavHome" extClass="custom-nav"></navigation-bar>
  </view>

  <!-- 导航栏占位元素 - 动态计算高度 -->
  <view class="nav-placeholder" style="height: {{navPlaceholderHeight}}"></view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{isEmpty}}" class="empty-container">
    <t-icon name="call" size="120rpx" color="#999"></t-icon>
    <view class="empty-text">暂无拨号记录</view>
    <view class="empty-desc">拨打电话后会在这里显示记录</view>
  </view>

  <!-- 主内容区 -->
  <view class="content-container">
    <!-- 记录列表 -->
    <view wx:if="{{!loading && !isEmpty}}" class="records-list">
      <view
        wx:for="{{callRecords}}"
        wx:key="_id"
        class="record-item"
      >
        <!-- 记录内容 -->
        <view class="record-content" bindtap="viewPostDetail" data-post-id="{{item.postId}}" data-post-type="{{item.postType}}" data-record-id="{{item._id}}">
          <view class="record-header">
            <view class="post-title">{{item.postTitle}}</view>
            <view class="phone-number">📞 {{item.phoneNumber}}</view>
          </view>

          <view class="record-body">
            <!-- 这里可以放其他内容 -->
          </view>

          <view class="record-footer">
            <view class="call-time-full">{{item.callTimeFormatted}}</view>
            <view class="post-type-tag">{{item.postType === 'supply' ? '供应' : '需求'}}</view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="record-actions">
          <view
            class="action-btn call-btn"
            bindtap="makeCall"
            data-phone="{{item.phoneNumber}}"
          >
            再次拨打
          </view>
          <view
            class="action-btn delete-btn"
            bindtap="deleteRecord"
            data-record-id="{{item._id}}"
          >
            删除
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
