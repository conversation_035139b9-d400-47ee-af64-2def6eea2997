// 云函数 - createPayment
const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 配置信息 - 请替换为你的实际配置
const config = {
  appid: 'wx996c8705b8b131e4', // 小程序AppID
  mchid: '1722224105', // 商户号
  serial_no: '2E5B0C10CEA6BA89EF6C190808C0479CE2E67CC2', // 证书序列号
  apiv3_private_key: '15700526449td482471813td48247181', // APIv3密钥
  // 商户私钥内容（字符串形式）
  private_key: `************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  `
}

/**
 * 生成随机字符串
 */
function generateNonceStr() {
  return Math.random().toString(36).substring(2, 17)
}

/**
 * 生成订单号
 */
function generateOrderNo() {
  const now = new Date()
  const timestamp = now.getTime()
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `RECHARGE_${timestamp}_${random}`
}

/**
 * 生成签名
 */
function generateSignature(method, url, timestamp, nonce_str, body) {
  const message = `${method}\n${url}\n${timestamp}\n${nonce_str}\n${body}\n`

  // 使用商户私钥
  const privateKey = config.private_key

  // 使用私钥签名
  const sign = crypto.createSign('RSA-SHA256')
  sign.update(message)
  return sign.sign(privateKey, 'base64')
}

/**
 * 构建Authorization头
 */
function buildAuthorizationHeader(method, url, body) {
  const timestamp = Math.floor(Date.now() / 1000)
  const nonce_str = generateNonceStr()
  const signature = generateSignature(method, url, timestamp, nonce_str, body)
  
  return `WECHATPAY2-SHA256-RSA2048 mchid="${config.mchid}",nonce_str="${nonce_str}",signature="${signature}",timestamp="${timestamp}",serial_no="${config.serial_no}"`
}

/**
 * 调用微信支付API
 */
async function callWechatPayAPI(orderData) {
  const url = '/v3/pay/transactions/jsapi'
  const method = 'POST'
  const body = JSON.stringify(orderData)
  
  const authorization = buildAuthorizationHeader(method, url, body)
  
  return new Promise((resolve, reject) => {
    const https = require('https')
    const options = {
      hostname: 'api.mch.weixin.qq.com',
      port: 443,
      path: url,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'WeChatPay-APIv3-SDK/Node.js',
        'Authorization': authorization,
        'Content-Length': Buffer.byteLength(body)
      }
    }
    
    const req = https.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data)
          if (res.statusCode === 200) {
            resolve(result)
          } else {
            reject(new Error(`微信支付API调用失败: ${data}`))
          }
        } catch (error) {
          reject(new Error(`解析响应失败: ${error.message}`))
        }
      })
    })
    
    req.on('error', (error) => {
      reject(error)
    })
    
    req.write(body)
    req.end()
  })
}

/**
 * 生成小程序支付参数
 */
function generateMiniProgramPayParams(prepay_id) {
  const timestamp = Math.floor(Date.now() / 1000).toString()
  const nonce_str = generateNonceStr()
  const package_str = `prepay_id=${prepay_id}`
  
  // 构建签名字符串
  const signStr = `${config.appid}\n${timestamp}\n${nonce_str}\n${package_str}\n`

  // 使用商户私钥
  const privateKey = config.private_key

  // 生成签名
  const sign = crypto.createSign('RSA-SHA256')
  sign.update(signStr)
  const paySign = sign.sign(privateKey, 'base64')
  
  return {
    timeStamp: timestamp,
    nonceStr: nonce_str,
    package: package_str,
    signType: 'RSA',
    paySign: paySign
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const { amount, points, description } = event

    // 调试信息
    console.log('云函数context:', JSON.stringify(context))
    console.log('event参数:', JSON.stringify(event))

    const openid = process.env.WX_OPENID || context.OPENID || event.userInfo?.openId
    
    console.log('当前登录用户的openid:', openid)

    if (!openid) {
      console.error('获取用户openid失败，context.OPENID为空')
      console.error('完整context:', context)
      throw new Error('用户未登录，请重新登录后再试')
    }

    console.log('用户openid:', openid)
    
    // 生成订单号
    const out_trade_no = generateOrderNo()
    
    // 构建订单数据
    const orderData = {
      appid: config.appid,
      mchid: config.mchid,
      description: description || `积分充值${points}积分`,
      out_trade_no: out_trade_no,
      notify_url: 'https://miaomuzhongxin-0giu90bpa4cbeaf5-1361723888.ap-shanghai.app.tcloudbase.com/', // 需要替换为实际的HTTP触发器地址
      amount: {
        total: amount * 100, // 金额单位：分
        currency: 'CNY'
      },
      payer: {
        openid: openid
      }
    }
    
    console.log('创建支付订单:', orderData)
    
    // 调用微信支付API
    const payResult = await callWechatPayAPI(orderData)
    
    console.log('微信支付API响应:', payResult)
    
    // 生成小程序支付参数
    const payParams = generateMiniProgramPayParams(payResult.prepay_id)

    // 保存订单信息到数据库 - 暂时注释，后期需要时再打开
    /*
    const db = cloud.database()
    await db.collection('payment_orders').add({
      data: {
        out_trade_no: out_trade_no,
        openid: openid,
        amount: amount,
        points: points,
        status: 'pending',
        prepay_id: payResult.prepay_id,
        create_time: new Date(),
        description: orderData.description
      }
    })
    */

    return {
      success: true,
      out_trade_no: out_trade_no,
      ...payParams
    }
    
  } catch (error) {
    console.error('创建支付订单失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
