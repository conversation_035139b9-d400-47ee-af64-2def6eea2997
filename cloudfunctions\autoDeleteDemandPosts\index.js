// 云函数 - autoDeleteDemandPosts
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    console.log('开始执行自动删除过期需求帖子任务');
    
    // 获取当前日期
    const currentDate = new Date();
    
    // 查询所有需求帖子，获取它们的创建时间和outDate
    const demands = await getAllDemands();
    
    console.log(`查询到需求帖子总数: ${demands.length}`);
    
    const expiredDemands = [];
    
    // 检查每个帖子是否过期
    demands.forEach(demand => {
      // 获取创建时间
      const createTime = new Date(demand.createTime);

      // 获取过期天数 (outDate格式为 "1天", "2天" 等)
      const outDays = parseInt(demand.outDate) || 1; // 默认为1天

      // 计算过期日期 (创建时间 + outDate天数)
      const expireDate = new Date(createTime);
      expireDate.setDate(expireDate.getDate() + outDays);

      // 如果当前日期大于过期日期，则帖子已过期
      if (currentDate > expireDate) {
        expiredDemands.push(demand);
      }
    });
    
    console.log(`发现过期需求帖子数: ${expiredDemands.length}`);
    
    // 如果没有过期帖子，直接返回
    if (expiredDemands.length === 0) {
      return {
        success: true,
        message: '没有需要删除的过期需求帖子'
      };
    }
    
    // 删除过期帖子及其图片
    const deleteResults = await deleteExpiredDemands(expiredDemands);
    
    return {
      success: true,
      deletedCount: deleteResults.length,
      message: `成功删除${deleteResults.length}条过期需求帖子及其图片`
    };
  } catch (error) {
    console.error('删除过期需求帖子失败', error);
    return {
      success: false,
      error: error
    };
  }
};

/**
 * 获取所有需求帖子
 */
async function getAllDemands() {
  const MAX_LIMIT = 100;
  // 先取得集合记录总数
  const countResult = await db.collection('demand_content').count();
  const total = countResult.total;
  
  // 计算需分几次取
  const batchTimes = Math.ceil(total / MAX_LIMIT);
  
  // 承载所有读操作的 promise 的数组
  const tasks = [];
  
  for (let i = 0; i < batchTimes; i++) {
    const promise = db.collection('demand_content')
      .skip(i * MAX_LIMIT)
      .limit(MAX_LIMIT)
      .get();
    tasks.push(promise);
  }
  
  // 等待所有
  const results = await Promise.all(tasks);
  
  // 合并数据
  let demands = [];
  results.forEach(result => {
    demands = demands.concat(result.data);
  });
  
  return demands;
}

/**
 * 删除过期需求帖子及其图片
 * @param {Array} expiredDemands 过期的需求帖子数组
 */
async function deleteExpiredDemands(expiredDemands) {
  const deleteResults = [];

  for (const demand of expiredDemands) {
    try {
      // 1. 删除回价相关资源
      await deleteDemandReplies(demand._id);

      // 2. 删除云存储中的求购图片
      if (demand.imageList && demand.imageList.length > 0) {
        await deletePostImages(demand);
      }

      // 3. 删除数据库中的帖子记录
      await db.collection('demand_content').doc(demand._id).remove();

      // 4. 尝试删除相关的通知记录
      try {
        await db.collection('notice')
          .where({
            postId: demand._id,
            postType: 'demand'
          })
          .remove();
      } catch (noticeError) {
        console.error(`删除帖子 ${demand._id} 的通知记录失败:`, noticeError);
      }

      console.log(`成功删除帖子: ${demand._id}, 标题: ${demand.title}`);
      deleteResults.push(demand._id);
    } catch (error) {
      console.error(`删除帖子 ${demand._id} 失败:`, error);
    }
  }

  return deleteResults;
}

/**
 * 删除求购相关的回价资源
 * @param {string} demandId 求购ID
 */
async function deleteDemandReplies(demandId) {
  try {
    // 1. 删除回价文件夹（整个目录）
    await deleteDemandReplyFolder(demandId);

    // 2. 删除回价数据库记录
    const replyResult = await db.collection('demand_reply')
      .where({
        demand_PostId: demandId
      })
      .get();

    console.log(`求购 ${demandId} 找到回价记录数量: ${replyResult.data.length}`);

    if (replyResult.data.length > 0) {
      const deletePromises = replyResult.data.map(reply => {
        return db.collection('demand_reply').doc(reply._id).remove();
      });

      await Promise.all(deletePromises);
      console.log(`成功删除求购 ${demandId} 的 ${replyResult.data.length} 条回价记录`);
    }

  } catch (error) {
    console.error(`删除求购 ${demandId} 的回价资源失败:`, error);
  }
}

/**
 * 删除回价文件夹（整个目录）
 * @param {string} demandId 求购ID
 */
async function deleteDemandReplyFolder(demandId) {
  try {
    // 查询该求购的所有回价记录，获取文件列表
    const replyResult = await db.collection('demand_reply')
      .where({
        demand_PostId: demandId
      })
      .get();

    // 收集所有回价图片文件ID
    let allImageFiles = [];
    replyResult.data.forEach(reply => {
      if (reply.imageList && Array.isArray(reply.imageList)) {
        allImageFiles = allImageFiles.concat(reply.imageList);
      }
    });

    console.log(`求购 ${demandId} 找到回价图片文件数量: ${allImageFiles.length}`);

    // 删除整个 demand_replyImages/demandPost_{demandId} 目录下的所有文件
    if (allImageFiles.length > 0) {
      await cloud.deleteFile({
        fileList: allImageFiles
      });
      console.log(`成功删除求购 ${demandId} 的回价文件夹，删除 ${allImageFiles.length} 个文件`);
    }

  } catch (error) {
    console.error(`删除求购 ${demandId} 的回价文件夹失败:`, error);
  }
}

/**
 * 删除帖子关联的云存储图片
 * @param {Object} post 帖子对象
 */
async function deletePostImages(post) {
  const userId = post.uid;
  const postId = post._id;

  // 根据命名规则，帖子图片存储在 demand_post/${userId}_${postId}/ 目录下
  // 获取目录下所有文件
  try {
    // 使用云函数获取指定目录下的所有文件
    const fileListResult = await cloud.getTempFileURL({
      fileList: post.imageList
    });

    // 准备要删除的文件ID列表
    const fileIDsToDelete = post.imageList;

    if (fileIDsToDelete.length > 0) {
      // 删除云存储中的文件
      await cloud.deleteFile({
        fileList: fileIDsToDelete
      });

      console.log(`成功删除帖子 ${postId} 的 ${fileIDsToDelete.length} 张图片`);
    }
  } catch (error) {
    console.error(`删除帖子 ${postId} 的图片失败:`, error);
    throw error;
  }
}