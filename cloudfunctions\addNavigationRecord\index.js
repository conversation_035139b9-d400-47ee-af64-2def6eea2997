// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { 
      postId, 
      postTitle, 
      latitude, 
      longitude, 
      address 
    } = event

    // 参数验证
    if (!postId || !postTitle || !latitude || !longitude) {
      return {
        success: false,
        message: '缺少必要参数'
      }
    }

    // 创建GeoPoint
    const location = db.Geo.Point(longitude, latitude)

    // 创建导航记录
    const navigationRecord = {
      userId: wxContext.OPENID, // 使用用户的openid
      postId: postId,
      postType: 'supply', // 只有supply有导航
      postTitle: postTitle,
      location: location, // 使用GeoPoint类型
      address: address || '',
      navigationTime: db.serverDate(),
      createTime: db.serverDate()
    }

    // 保存到数据库
    const result = await db.collection('navigation_records').add({
      data: navigationRecord
    })

    return {
      success: true,
      message: '导航记录保存成功',
      recordId: result._id
    }

  } catch (error) {
    console.error('保存导航记录失败:', error)
    return {
      success: false,
      message: '保存导航记录失败',
      error: error.message
    }
  }
}
