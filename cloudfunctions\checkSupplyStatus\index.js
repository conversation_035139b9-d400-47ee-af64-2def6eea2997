// 云函数 - checkSupplyStatus
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 计算日期时间差（天数）
function daysDiff(date1, date2) {
  const oneDay = 24 * 60 * 60 * 1000 // 一天的毫秒数
  const diffTime = Math.abs(date2 - date1)
  const diffDays = Math.ceil(diffTime / oneDay)
  return diffDays
}

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const now = new Date()
    // 根据触发器类型决定是深度检查还是增量检查
    const isDeepCheck = event.triggerName === 'weeklyDeepCheck'
    console.log(`开始执行${isDeepCheck ? '深度' : '增量'}检查，当前时间：`, now)
    
    // 测试阶段设置为10秒，正式环境设置为180天
    const expiryThreshold = 15552000 // 单位：秒
    
    let query = {}
    
    // 增量检查只处理接近180天的记录
    if (!isDeepCheck) {
      // 计算175天前的时间点（提前5天检查接近过期的记录）
      const nearExpiryDate = new Date()
      //nearExpiryDate.setSeconds(nearExpiryDate.getSeconds() - expiryThreshold + 60*60*24*5) // 提前5天检查
      nearExpiryDate.setSeconds(nearExpiryDate.getSeconds() - (expiryThreshold - 60*60*24*5)) // 175天前

      query = {
        status: 'active',  // 只检查活跃状态的记录
        photoUpdated: _.lt(nearExpiryDate)  // 照片更新时间在175天前（即接近180天过期）
      }
    } else {
      // 深度检查处理所有非停用的记录deactivated
      query = {
        status: _.neq('deactivated')
      }
    }
    
    let processedCount = 0
    let deactivatedCount = 0
    
    // 使用分页处理，避免一次性处理太多数据
    const batchSize = 100
    let hasMore = true
    let lastId = null
    
    while (hasMore) {
      let currentQuery = { ...query }
      
      // 如果有上一批次的最后ID，则添加到查询条件
      if (lastId) {
        currentQuery._id = _.gt(lastId)
      }
      
      // 查询需要检查的记录
      const records = await db.collection('supply_content')
        .where(currentQuery)
        .orderBy('_id', 'asc')
        .limit(batchSize)
        .get()
      
      const items = records.data
      processedCount += items.length
      
      console.log(`本批次获取到 ${items.length} 条记录`)
      
      if (items.length === 0) {
        hasMore = false
        break
      }
      
      // 记录最后一条ID，用于下一次查询
      lastId = items[items.length - 1]._id
      
      // 处理记录
      const updatePromises = []
      
      for (const item of items) {
        // 检查photoUpdated是否超过阈值
        if (item.photoUpdated) {
          const photoUpdatedDate = new Date(item.photoUpdated)
          // 对于测试，使用秒计算；实际环境用天数计算
          const timeDiff = (now - photoUpdatedDate) / 1000 // 秒数差
          
          if (timeDiff > expiryThreshold) {
            // 更新记录状态为deactivated
            updatePromises.push(
              db.collection('supply_content').doc(item._id).update({
                data: {
                  status: 'deactivated',
                  deactivatedTime: db.serverDate(),
                  deactivatedReason: 'auto_expired'
                }
              })
            )
            deactivatedCount++
          }
        }
      }
      
      // 等待所有更新操作完成
      if (updatePromises.length > 0) {
        await Promise.all(updatePromises)
        console.log(`本批次停用了 ${updatePromises.length} 条记录`)
      }
      
      // 如果返回的记录少于批次大小，说明没有更多记录了
      if (items.length < batchSize) {
        hasMore = false
      }
    }
    
    return {
      success: true,
      processedCount,
      deactivatedCount,
      message: `${isDeepCheck ? '深度' : '增量'}检查完成，共处理 ${processedCount} 条记录，停用 ${deactivatedCount} 条记录`
    }
    
  } catch (error) {
    console.error('检查失败', error)
    return {
      success: false,
      error: error.message
    }
  }
} 