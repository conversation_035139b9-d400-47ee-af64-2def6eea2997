/* components/welcome-screen/welcome-screen.wxss */
.welcome-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(to bottom, #ffffff, #a7e3a5, #4caf50);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
  /* 确保全屏显示并位于顶层 */
  bottom: 0;
  right: 0;
}

.welcome-container.visible {
  opacity: 1;
}

.welcome-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between; /* 改为space-between，使元素之间均匀分布 */
  text-align: center;
  padding: 60rpx 40rpx;
  box-sizing: border-box;
}

.countdown-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
  margin-bottom: 20rpx;
  z-index: 1000;
}

.countdown {
  font-size: 36rpx;
  font-weight: bold;
  color: #0a5c2f;
  background-color: #ffffff;
  border-radius: 40rpx;
  padding: 16rpx 40rpx;
  display: inline-block;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  border: 2rpx solid #0a5c2f;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 顶部区域样式 */
.welcome-top {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

/* 中间区域样式 */
.welcome-middle {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  justify-content: center;
}

/* 底部区域样式 */
.welcome-bottom {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
  flex: 1;
  justify-content: flex-end;
}

.logo-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}

.welcome-logo {
  width: 80vw; /* 使用视口宽度的百分比 */
  height: 30vh; /* 使用视口高度的百分比 */
  margin-bottom: 20rpx;
  object-fit: contain; /* 确保图片比例正确 */
}

.welcome-header {
  margin-bottom: 30rpx;
}

.welcome-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #0a5c2f;
  margin-bottom: 10rpx;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  font-size: 36rpx;
  color: #0a5c2f;
  margin-bottom: 20rpx;
}

.welcome-message {
  margin-bottom: 40rpx;
}

.message-text {
  font-size: 32rpx;
  color: #0a5c2f;
  margin-bottom: 10rpx;
}

.message-slogan {
  font-size: 28rpx;
  color: #0a5c2f;
}

.enter-btn {
  width: 600rpx;
  height: 90rpx;
  background-color: #0a5c2f;
  color: #ffffff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 270rpx; /* 增加底部间距 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  position: relative; /* 改为相对定位 */
} 