// 云函数 getNurseryList - 渲染苗圃转让页面
const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();
const _ = db.command;
const $ = db.command.aggregate;

// 计算两点之间的距离（使用Haversine公式）
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // 地球半径，单位：公里
  
  // 将角度转换为弧度
  const deg2rad = (deg) => deg * (Math.PI/180);
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  
  // Haversine公式
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // 距离，单位：公里
  
  return distance;
}

// 格式化距离显示
function formatDistance(distance) {
  if (distance === null || distance === undefined || isNaN(distance)) {
    return null;
  }
  
  if (distance < 1) {
    // 小于1公里，显示为米
    return `${Math.round(distance * 1000)}米`;
  } else if (distance < 10) {
    // 小于10公里，显示一位小数
    return `${distance.toFixed(1)}公里`;
  } else {
    // 大于等于10公里，显示整数
    return `${Math.round(distance)}公里`;
  }
}

// 格式化时间为多久之前
function formatTimeAgo(dateObj) {
  if (!dateObj) return '';
  
  // 如果是字符串，转换为Date对象
  const date = typeof dateObj === 'string' ? new Date(dateObj) : dateObj;
  const now = new Date();
  
  const diff = Math.floor((now - date) / 1000); // 差异，单位：秒
  
  if (diff < 60) {
    return '刚刚';
  } else if (diff < 3600) {
    return Math.floor(diff / 60) + '分钟前';
  } else if (diff < 86400) {
    return Math.floor(diff / 3600) + '小时前';
  } else if (diff < 2592000) {
    return Math.floor(diff / 86400) + '天前';
  } else if (diff < 31536000) {
    return Math.floor(diff / 2592000) + '个月前';
  } else {
    return Math.floor(diff / 31536000) + '年前';
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  // 从请求参数中获取分页信息和筛选条件
  const { page = 1, pageSize = 10, searchKeyword = '', selectedSize = '全部', selectedPrice = '全部', userLocation } = event;
  
  try {
    // 计算分页跳过的数量
    const skip = (page - 1) * pageSize;
    
    // 构建基本查询条件
    let query = {};
    
    // 搜索关键词筛选
    if (searchKeyword) {
      // 创建正则表达式，支持模糊查询
      const keywordRegex = db.RegExp({
        regexp: searchKeyword,
        options: 'i' // 不区分大小写
      });
      
      // 在多个字段中搜索关键词
      query = _.or([
        { title: keywordRegex },
        { address: keywordRegex },
        { des: keywordRegex }
      ]);
    }
    
    // 面积筛选
    if (selectedSize !== '全部') {
      let sizeFilter = {};
      switch (selectedSize) {
        case '小于50亩':
          sizeFilter = _.lt(50);
          break;
        case '50-100亩':
          sizeFilter = _.and(_.gte(50), _.lte(100));
          break;
        case '100-500亩':
          sizeFilter = _.and(_.gte(100), _.lte(500));
          break;
        case '500亩以上':
          sizeFilter = _.gt(500);
          break;
      }
      if (Object.keys(sizeFilter).length > 0) {
        if (Object.keys(query).length > 0) {
          query = _.and([query, { area: sizeFilter }]);
        } else {
          query.area = sizeFilter;
        }
      }
    }
    
    // 价格筛选
    if (selectedPrice !== '全部') {
      let priceFilter = {};
      switch (selectedPrice) {
        case '低于10万':
          priceFilter = _.lt(10);
          break;
        case '10-50万':
          priceFilter = _.and(_.gte(10), _.lte(50));
          break;
        case '50-100万':
          priceFilter = _.and(_.gte(50), _.lte(100));
          break;
        case '100万以上':
          priceFilter = _.gt(100);
          break;
        case '面议':
          priceFilter = 0; // 假设0表示面议
          break;
      }
      
      if (typeof priceFilter === 'object' && Object.keys(priceFilter).length > 0) {
        if (Object.keys(query).length > 0) {
          query = _.and([query, { price: priceFilter }]);
        } else {
          query.price = priceFilter;
        }
      } else if (priceFilter === 0) {
        if (Object.keys(query).length > 0) {
          query = _.and([query, { price: 0 }]);
        } else {
          query.price = 0;
        }
      }
    }
    
    // 分页查询 - 多获取一条数据判断是否有更多
    const extraLimit = pageSize + 1;
    
    // 创建查询构建器
    let queryBuilder = db.collection('nursery_content').where(query);
    
    // 按照拍照更新时间降序排序
    queryBuilder = queryBuilder.orderBy('photoUpdated', 'desc');
    
    // 应用分页
    const queryResult = await queryBuilder.skip(skip).limit(extraLimit).get();
    
    const rawData = queryResult.data;
    
    // 判断是否有更多数据
    const hasMore = rawData.length > pageSize;
    
    // 如果多获取了一条数据，需要去掉最后一条
    const resultData = hasMore ? rawData.slice(0, pageSize) : rawData;
    
    // 处理返回数据
    let processedData = resultData.map(item => {
      // 计算时间 - 优先使用photoUpdated字段
      const timeDisplayDate = item.photoUpdated || item.createTime;
      const timeAgo = formatTimeAgo(timeDisplayDate);
      
      // 格式化距离显示
      let calculatedDistance = null;
      let distanceValue = null;
      
      // 如果有用户位置和苗圃位置，计算距离
      if (userLocation && item.location) {
        try {
          // 获取用户位置坐标
          let userLat, userLon;
          if (userLocation.coordinates && Array.isArray(userLocation.coordinates) && userLocation.coordinates.length >= 2) {
            userLon = userLocation.coordinates[0];
            userLat = userLocation.coordinates[1];
          } else if (userLocation.latitude !== undefined && userLocation.longitude !== undefined) {
            userLat = userLocation.latitude;
            userLon = userLocation.longitude;
          }
          
          // 获取苗圃位置坐标
          let itemLat, itemLon;
          if (item.location && item.location.coordinates && Array.isArray(item.location.coordinates) && item.location.coordinates.length >= 2) {
            itemLon = item.location.coordinates[0];
            itemLat = item.location.coordinates[1];
          } else if (item.location && item.location.latitude !== undefined && item.location.longitude !== undefined) {
            itemLat = item.location.latitude;
            itemLon = item.location.longitude;
          }
          
          // 如果有有效坐标，计算距离
          if (userLat && userLon && itemLat && itemLon) {
            distanceValue = calculateDistance(userLat, userLon, itemLat, itemLon);
            calculatedDistance = "距离你" + formatDistance(distanceValue);
          }
        } catch (error) {
          console.error(`计算距离出错:`, error);
        }
      }
      
      // 返回处理后的数据
      return {
        _id: item._id,
        title: item.title || '未命名苗圃',
        location: item.address || '未知位置',
        area: item.area || 0,
        price: item.price,
        description: item.des || '',
        des: item.des || '',
        imageList: item.imageList || [],
        createTime: item.createTime,
        photoUpdated: item.photoUpdated,
        createTimeText: timeAgo,
        calculatedDistance: calculatedDistance,
        distanceValue: distanceValue,
        viewCount: item.viewCount || 0
      };
    });
    
    // 返回结果
    return {
      code: 0,
      msg: '获取数据成功',
      data: processedData,
      page: page,
      pageSize: pageSize,
      hasMore: hasMore
    };
  } catch (error) {
    console.error('获取苗圃列表失败:', error);
    return {
      code: -1,
      msg: '获取数据失败',
      error: error.message
    };
  }
}; 