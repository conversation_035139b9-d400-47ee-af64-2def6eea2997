// 收藏功能工具类 - 简化版本
var CollectionUtils = {
  // 本地缓存收藏状态
  collectionCache: {
    supply: new Set(),
    demand: new Set(),
    lastUpdate: null
  },

  // 缓存失效时间：10分钟
  CACHE_EXPIRE_TIME: 10 * 60 * 1000,

  // 缓存大小限制
  MAX_CACHE_SIZE: 200,

  // 单个查询缓存
  singleQueryCache: new Map(),
  SINGLE_CACHE_EXPIRE_TIME: 10 * 60 * 1000,
  MAX_SINGLE_CACHE_SIZE: 500,

  // 初始化标志
  initialized: false,

  /**
   * 初始化方法
   */
  init: function() {
    if (!this.initialized) {
      this.startCacheCleanup()
      this.initialized = true
    }
  },

  /**
   * 启动缓存清理定时器
   */
  startCacheCleanup: function() {
    var self = this
    // 每30秒清理一次过期缓存
    setInterval(function() {
      self.cleanupExpiredSingleCache()
    }, 30 * 1000)
  },

  /**
   * 清理过期的单个查询缓存
   */
  cleanupExpiredSingleCache: function() {
    var now = Date.now()
    var cleanedCount = 0
    var keysToDelete = []

    // 收集需要删除的键
    this.singleQueryCache.forEach(function(value, key) {
      if (now - value.timestamp > this.SINGLE_CACHE_EXPIRE_TIME) {
        keysToDelete.push(key)
      }
    }.bind(this))

    // 删除过期项
    keysToDelete.forEach(function(key) {
      this.singleQueryCache.delete(key)
      cleanedCount++
    }.bind(this))

    if (cleanedCount > 0) {
      console.log('清理了 ' + cleanedCount + ' 个过期的单个查询缓存，当前缓存大小: ' + this.singleQueryCache.size)
    }
  },

  /**
   * 检查单个查询缓存是否有效
   */
  isSingleCacheValid: function(timestamp) {
    return Date.now() - timestamp < this.SINGLE_CACHE_EXPIRE_TIME
  },

  /**
   * 设置单个查询缓存（带大小限制）
   */
  setSingleCache: function(key, value) {
    // 检查大小限制
    if (this.singleQueryCache.size >= this.MAX_SINGLE_CACHE_SIZE) {
      // 删除最老的项目（FIFO策略）
      var firstKey = this.singleQueryCache.keys().next().value
      this.singleQueryCache.delete(firstKey)
      console.log('单个查询缓存已满，删除最老项目: ' + firstKey)
    }

    this.singleQueryCache.set(key, {
      isCollected: value.isCollected,
      timestamp: Date.now()
    })
  },

  /**
   * 智能添加到缓存（带大小限制）
   */
  addToCache: function(itemType, itemId) {
    var cache = this.collectionCache[itemType]

    // 检查缓存大小限制
    if (cache.size >= this.MAX_CACHE_SIZE) {
      // 简单的清理策略：清除一半缓存
      var itemsToRemove = Math.floor(this.MAX_CACHE_SIZE / 2)
      var iterator = cache.values()
      for (var i = 0; i < itemsToRemove; i++) {
        var item = iterator.next()
        if (!item.done) {
          cache.delete(item.value)
        }
      }
    }

    cache.add(itemId)
  },

  /**
   * 检查缓存是否有效
   */
  isCacheValid: function() {
    if (!this.collectionCache.lastUpdate) {
      return false
    }
    return Date.now() - this.collectionCache.lastUpdate < this.CACHE_EXPIRE_TIME
  },

  /**
   * 获取缓存状态信息（用于调试）
   */
  getCacheStatus: function() {
    return {
      mainCache: {
        supply: this.collectionCache.supply.size,
        demand: this.collectionCache.demand.size,
        lastUpdate: this.collectionCache.lastUpdate
      },
      singleCache: {
        size: this.singleQueryCache.size,
        maxSize: this.MAX_SINGLE_CACHE_SIZE,
        usage: Math.round(this.singleQueryCache.size / this.MAX_SINGLE_CACHE_SIZE * 100) + '%'
      }
    }
  },

  /**
   * 切换收藏状态
   */
  toggleCollection: function(itemId, itemType, itemData) {
    var self = this
    return new Promise(function(resolve, reject) {
      wx.showLoading({
        title: '处理中...',
        mask: true
      })

      wx.cloud.callFunction({
        name: 'collectionManager',
        data: {
          action: 'toggle',
          itemId: itemId,
          itemType: itemType,
          itemData: itemData
        },
        success: function(result) {
          wx.hideLoading()
          if (result.result.success) {
            // 更新本地缓存
            if (result.result.isCollected) {
              self.collectionCache[itemType].add(itemId)
            } else {
              self.collectionCache[itemType].delete(itemId)
            }

            // 通知其他页面更新收藏状态
            self.notifyCollectionStatusChange(itemId, itemType, result.result.isCollected)

            wx.showToast({
              title: result.result.message,
              icon: 'success',
              duration: 1500
            })
            resolve(result.result)
          } else {
            wx.showToast({
              title: result.result.message || '操作失败',
              icon: 'none'
            })
            resolve(result.result)
          }
        },
        fail: function(error) {
          wx.hideLoading()
          console.error('收藏操作失败:', error)
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          })
          resolve({ success: false, message: '网络错误' })
        }
      })
    })
  },

  /**
   * 检查单个收藏状态（优化版）
   */
  checkSingleCollectionStatus: function(itemId, itemType) {
    var self = this
    var cacheKey = itemType + '_' + itemId

    return new Promise(function(resolve, reject) {
      // 1. 检查内存缓存
      if (self.collectionCache[itemType].has(itemId)) {
        resolve(true)
        return
      }

      // 2. 检查单个查询缓存
      var singleCached = self.singleQueryCache.get(cacheKey)
      if (singleCached && self.isSingleCacheValid(singleCached.timestamp)) {
        resolve(singleCached.isCollected)
        return
      }

      // 3. 调用云函数进行单个查询
      wx.cloud.callFunction({
        name: 'collectionManager',
        data: {
          action: 'checkSingle',
          itemId: itemId,
          itemType: itemType
        },
        success: function(result) {
          if (result.result.success) {
            var isCollected = result.result.isCollected

            // 更新单个查询缓存
            self.setSingleCache(cacheKey, {
              isCollected: isCollected
            })

            // 如果已收藏，也更新主缓存
            if (isCollected) {
              self.addToCache(itemType, itemId)
            }

            resolve(isCollected)
          } else {
            resolve(false)
          }
        },
        fail: function(error) {
          console.error('检查单个收藏状态失败:', error)
          resolve(false)
        }
      })
    })
  },

  /**
   * 批量检查收藏状态（优化版）
   */
  batchCheckCollectionStatus: function(itemIds, itemType, useSmartCache) {
    if (useSmartCache === undefined) useSmartCache = true

    if (!itemIds || itemIds.length === 0) {
      return Promise.resolve([])
    }

    if (useSmartCache) {
      return this.smartBatchCheck(itemIds, itemType)
    }

    // 原有逻辑（保持兼容性）
    var self = this
    if (this.isCacheValid()) {
      var result = itemIds.filter(function(id) {
        return self.collectionCache[itemType].has(id)
      })
      return Promise.resolve(result)
    }

    return new Promise(function(resolve, reject) {
      wx.cloud.callFunction({
        name: 'collectionManager',
        data: {
          action: 'batchCheck',
          itemIds: itemIds,
          itemType: itemType
        },
        success: function(result) {
          if (result.result.success) {
            // 更新缓存
            self.collectionCache[itemType].clear()
            result.result.data.forEach(function(id) {
              self.collectionCache[itemType].add(id)
            })
            self.collectionCache.lastUpdate = Date.now()

            resolve(result.result.data)
          } else {
            resolve([])
          }
        },
        fail: function(error) {
          console.error('批量检查收藏状态失败:', error)
          resolve([])
        }
      })
    })
  },

  /**
   * 智能批量检查（只查询未缓存的ID）
   */
  smartBatchCheck: function(itemIds, itemType) {
    var self = this
    var cachedIds = []
    var uncachedIds = []

    itemIds.forEach(function(id) {
      if (self.collectionCache[itemType].has(id)) {
        cachedIds.push(id)
      } else {
        // 检查单个查询缓存
        var cacheKey = itemType + '_' + id
        var singleCached = self.singleQueryCache.get(cacheKey)
        if (singleCached && self.isSingleCacheValid(singleCached.timestamp)) {
          if (singleCached.isCollected) {
            cachedIds.push(id)
          }
        } else {
          uncachedIds.push(id)
        }
      }
    })

    // 如果全部命中缓存，直接返回
    if (uncachedIds.length === 0) {
      return Promise.resolve(cachedIds)
    }

    return new Promise(function(resolve, reject) {
      wx.cloud.callFunction({
        name: 'collectionManager',
        data: {
          action: 'checkSpecific',
          itemIds: uncachedIds,
          itemType: itemType
        },
        success: function(result) {
          if (result.result.success) {
            var newCollectedIds = result.result.data

            // 更新缓存（增量更新，不清空）
            newCollectedIds.forEach(function(id) {
              self.addToCache(itemType, id)
            })

            // 更新单个查询缓存
            uncachedIds.forEach(function(id) {
              var cacheKey = itemType + '_' + id
              var isCollected = newCollectedIds.indexOf(id) !== -1
              self.setSingleCache(cacheKey, {
                isCollected: isCollected
              })
            })

            resolve(cachedIds.concat(newCollectedIds))
          } else {
            resolve(cachedIds)
          }
        },
        fail: function(error) {
          console.error('智能批量检查失败:', error)
          resolve(cachedIds)
        }
      })
    })
  },

  /**
   * 获取收藏列表
   */
  getCollectionList: function(itemType, page, pageSize) {
    if (itemType === undefined) itemType = 'all'
    if (page === undefined) page = 1
    if (pageSize === undefined) pageSize = 10

    return new Promise(function(resolve, reject) {
      wx.cloud.callFunction({
        name: 'collectionManager',
        data: {
          action: 'list',
          itemType: itemType,
          page: page,
          pageSize: pageSize
        },
        success: function(result) {
          if (result.result.success) {
            resolve(result.result)
          } else {
            reject(new Error(result.result.message || '获取收藏列表失败'))
          }
        },
        fail: function(error) {
          console.error('获取收藏列表失败:', error)
          reject(error)
        }
      })
    })
  },

  /**
   * 删除收藏
   */
  removeCollection: function(itemId, itemType) {
    var self = this
    return new Promise(function(resolve, reject) {
      wx.showLoading({
        title: '删除中...',
        mask: true
      })

      wx.cloud.callFunction({
        name: 'collectionManager',
        data: {
          action: 'remove',
          itemId: itemId,
          itemType: itemType
        },
        success: function(result) {
          wx.hideLoading()
          if (result.result.success) {
            // 更新本地缓存
            self.collectionCache[itemType].delete(itemId)

            // 清除单个收藏状态缓存，确保详情页状态更新
            self.clearSingleCollectionCache(itemId, itemType)

            // 通知其他页面更新收藏状态
            self.notifyCollectionStatusChange(itemId, itemType, false)

            wx.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 1500
            })
            resolve(result.result)
          } else {
            wx.showToast({
              title: result.result.message || '删除失败',
              icon: 'none'
            })
            resolve(result.result)
          }
        },
        fail: function(error) {
          wx.hideLoading()
          console.error('删除收藏失败:', error)
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          })
          resolve({ success: false, message: '网络错误' })
        }
      })
    })
  },

  /**
   * 清除单个收藏状态缓存
   */
  clearSingleCollectionCache: function(itemId, itemType) {
    // 清除内存缓存
    if (this.collectionCache[itemType]) {
      this.collectionCache[itemType].delete(itemId)
    }

    // 清除本地存储中的单个状态缓存（如果有的话）
    var statusCacheKey = 'collection_status_' + itemType + '_' + itemId
    wx.removeStorageSync(statusCacheKey)

    // 清除本地存储中的收藏列表缓存
    var cacheKey = itemType + '_collections'
    var cachedIds = wx.getStorageSync(cacheKey) || []
    var index = cachedIds.indexOf(itemId)
    if (index > -1) {
      cachedIds.splice(index, 1)
      wx.setStorageSync(cacheKey, cachedIds)
    }

    console.log('已清除收藏状态缓存:', itemType, itemId)
  },

  /**
   * 检查单个项目的收藏状态（同步版本）
   */
  isCollected: function(itemId, itemType) {
    return this.collectionCache[itemType].has(itemId)
  },

  /**
   * 清除缓存
   */
  clearCache: function() {
    this.collectionCache.supply.clear()
    this.collectionCache.demand.clear()
    this.collectionCache.lastUpdate = null
    this.singleQueryCache.clear()
  },

  /**
   * 通知其他页面收藏状态变化
   */
  notifyCollectionStatusChange: function(itemId, itemType, isCollected) {
    const pages = getCurrentPages()

    // 通知detail页面更新状态
    pages.forEach(page => {
      // 通知supply detail页面
      if (page.route === 'pages/supply/detail/detail' &&
          itemType === 'supply' &&
          page.data.id === itemId &&
          typeof page.updateCollectionStatus === 'function') {
        page.updateCollectionStatus(isCollected)
      }

      // 通知demand detail页面
      if (page.route === 'pages/demand/detail/detail' &&
          itemType === 'demand' &&
          page.data.id === itemId &&
          typeof page.updateCollectionStatus === 'function') {
        page.updateCollectionStatus(isCollected)
      }

      // 注意：不通知myCollection页面，因为它有自己的删除逻辑
      // 避免重复删除导致数据异常
    })
  },

  /**
   * 格式化收藏项目数据
   */
  formatCollectionItem: function(item) {
    // 处理图片URL，支持对象和字符串两种格式
    var imageUrl = item.itemImageUrl;
    if (typeof imageUrl === 'object' && imageUrl && imageUrl.url) {
      imageUrl = imageUrl.url;
    } else if (typeof imageUrl !== 'string') {
      imageUrl = '';
    }

    return {
      id: item.itemId,
      type: item.itemType,
      title: item.itemTitle,
      price: item.itemPrice,
      imageUrl: imageUrl,
      address: item.itemAddress,
      contactName: item.itemContactName,
      collectionTime: item.createTime,
      status: item.itemStatus,
      content: item.itemContent || '', // 添加简介内容
      formattedPrice: item.itemPrice ? '¥' + item.itemPrice : '',
      formattedTime: this.formatTime(item.createTime)
    }
  },

  /**
   * 格式化时间显示
   */
  formatTime: function(time) {
    if (!time) return ''

    var date = new Date(time)
    var now = new Date()
    var diff = now - date

    var minute = 60 * 1000
    var hour = 60 * minute
    var day = 24 * hour

    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前'
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前'
    } else if (diff < 7 * day) {
      return Math.floor(diff / day) + '天前'
    } else {
      return date.toLocaleDateString()
    }
  }
}

// 初始化
CollectionUtils.init()

module.exports = CollectionUtils