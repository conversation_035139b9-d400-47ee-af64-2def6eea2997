// 列表页面收藏状态辅助工具
const collectionUtils = require('./collectionUtils.js');

class ListCollectionHelper {
  /**
   * 为列表项添加收藏状态（优化版）
   * @param {Array} items 列表项数组
   * @param {string} itemType 项目类型
   * @returns {Promise<Array>} 带收藏状态的列表项
   */
  static async addCollectionStatusToList(items, itemType) {
    if (!items || items.length === 0) {
      return items;
    }

    try {
      // 提取所有ID
      const itemIds = items.map(item => item.id);
      
      // 使用智能批量检查
      const collectedIds = await collectionUtils.batchCheckCollectionStatus(itemIds, itemType, true);
      
      // 为每个项目添加收藏状态
      return items.map(item => ({
        ...item,
        isCollected: collectedIds.includes(item.id)
      }));
    } catch (error) {
      console.error('添加收藏状态失败:', error);
      // 出错时返回原始数据，不影响列表显示
      return items.map(item => ({
        ...item,
        isCollected: false
      }));
    }
  }

  /**
   * 预加载下一页的收藏状态
   * @param {Array} nextPageItems 下一页的项目
   * @param {string} itemType 项目类型
   */
  static async preloadCollectionStatus(nextPageItems, itemType) {
    if (!nextPageItems || nextPageItems.length === 0) {
      return;
    }

    try {
      const itemIds = nextPageItems.map(item => item.id);
      // 静默预加载，不等待结果
      collectionUtils.batchCheckCollectionStatus(itemIds, itemType, true);
    } catch (error) {
      // 预加载失败不影响用户体验
      console.log('预加载收藏状态失败:', error);
    }
  }

  /**
   * 处理列表项收藏状态变化
   * @param {Array} items 当前列表项
   * @param {string} itemId 变化的项目ID
   * @param {boolean} isCollected 新的收藏状态
   * @returns {Array} 更新后的列表项
   */
  static updateListItemCollectionStatus(items, itemId, isCollected) {
    return items.map(item => {
      if (item.id === itemId) {
        return {
          ...item,
          isCollected: isCollected
        };
      }
      return item;
    });
  }
}

module.exports = ListCollectionHelper;
