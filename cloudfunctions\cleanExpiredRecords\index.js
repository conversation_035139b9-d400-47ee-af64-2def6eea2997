const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 清理过期记录云函数
 * 删除 call_records 和 navigation_records 集合中超过1分钟的记录
 * 通过定时触发器每分钟自动调用（测试模式）
 */
exports.main = async (event, context) => {
  console.log('开始清理过期记录...')
  
  try {
    // 计算1分钟前的时间（测试模式）
    const oneMinuteAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    console.log('清理时间点:', oneMinuteAgo.toISOString())
    
    let totalDeleted = 0
    
    // 清理过期的拨号记录
    console.log('开始清理过期拨号记录...')
    const callRecordsResult = await cleanExpiredCallRecords(oneMinuteAgo)
    totalDeleted += callRecordsResult.deleted

    // 清理过期的导航记录
    console.log('开始清理过期导航记录...')
    const navigationRecordsResult = await cleanExpiredNavigationRecords(oneMinuteAgo)
    totalDeleted += navigationRecordsResult.deleted
    
    console.log('清理完成，总共删除记录数:', totalDeleted)
    
    return {
      success: true,
      message: '过期记录清理完成',
      deletedCount: totalDeleted,
      callRecords: callRecordsResult,
      navigationRecords: navigationRecordsResult,
      cleanupTime: new Date().toISOString()
    }
    
  } catch (error) {
    console.error('清理过期记录失败:', error)
    return {
      success: false,
      message: '清理过期记录失败',
      error: error.message
    }
  }
}

/**
 * 清理过期的拨号记录
 */
async function cleanExpiredCallRecords(cutoffDate) {
  try {
    let deletedCount = 0
    let hasMore = true
    
    while (hasMore) {
      // 分批查询过期记录（每次最多20条）
      const expiredRecords = await db.collection('call_records')
        .where({
          createTime: db.command.lt(cutoffDate)
        })
        .limit(20)
        .get()
      
      if (expiredRecords.data.length === 0) {
        hasMore = false
        break
      }
      
      // 批量删除
      const deletePromises = expiredRecords.data.map(record => 
        db.collection('call_records').doc(record._id).remove()
      )
      
      const deleteResults = await Promise.all(deletePromises)
      const batchDeleted = deleteResults.filter(result => result.stats.removed > 0).length
      deletedCount += batchDeleted
      
      console.log(`拨号记录批次删除: ${batchDeleted}条`)
      
      // 如果本批次删除的记录少于查询的记录，说明没有更多记录了
      if (expiredRecords.data.length < 20) {
        hasMore = false
      }
    }
    
    console.log(`拨号记录清理完成，删除总数: ${deletedCount}条`)
    
    return {
      collection: 'call_records',
      deleted: deletedCount,
      cutoffDate: cutoffDate.toISOString()
    }
    
  } catch (error) {
    console.error('清理拨号记录失败:', error)
    throw error
  }
}

/**
 * 清理过期的导航记录
 */
async function cleanExpiredNavigationRecords(cutoffDate) {
  try {
    let deletedCount = 0
    let hasMore = true
    
    while (hasMore) {
      // 分批查询过期记录（每次最多20条）
      const expiredRecords = await db.collection('navigation_records')
        .where({
          createTime: db.command.lt(cutoffDate)
        })
        .limit(20)
        .get()
      
      if (expiredRecords.data.length === 0) {
        hasMore = false
        break
      }
      
      // 批量删除
      const deletePromises = expiredRecords.data.map(record => 
        db.collection('navigation_records').doc(record._id).remove()
      )
      
      const deleteResults = await Promise.all(deletePromises)
      const batchDeleted = deleteResults.filter(result => result.stats.removed > 0).length
      deletedCount += batchDeleted
      
      console.log(`导航记录批次删除: ${batchDeleted}条`)
      
      // 如果本批次删除的记录少于查询的记录，说明没有更多记录了
      if (expiredRecords.data.length < 20) {
        hasMore = false
      }
    }
    
    console.log(`导航记录清理完成，删除总数: ${deletedCount}条`)
    
    return {
      collection: 'navigation_records',
      deleted: deletedCount,
      cutoffDate: cutoffDate.toISOString()
    }
    
  } catch (error) {
    console.error('清理导航记录失败:', error)
    throw error
  }
}
