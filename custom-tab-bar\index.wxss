/* 自定义tabbar样式 */
.custom-tab-bar-wrapper {
  position: relative;
  width: 100%;
}

.custom-tab-bar {
  --td-tab-bar-border-color: transparent !important;
  --td-tab-bar-item-color: #666666 !important;
  --td-tab-bar-item-active-color: #2ba471 !important;
  --td-tab-bar-item-tag-active-color: #2ba471 !important;
  --td-tab-bar-active-color: #2ba471 !important;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
  display: flex !important;
  visibility: visible !important;
  background-color: #ffffff !important;
  width: 100% !important;
}

/* 中间的发布按钮 */
.center-publish-button {
  position: fixed;
  bottom: calc(30rpx + env(safe-area-inset-bottom));
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.publish-button-inner {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.publish-button-inner-hover {
  transform: scale(0.9);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.publish-icon {
  width: 95%;
  height: 95%;
}

.publish-text {
  font-size: 27rpx;
  color: #333333;
  margin-top: 6rpx;
  font-weight: 600;
}

/* 调整图标与文字间距 */
.custom-tab-bar .t-tab-bar-item__content {
  row-gap: 8rpx !important;
}

/* 调整文字大小 */
.custom-tab-bar .t-tab-bar-item__text {
  font-size: 26rpx !important;
  line-height: 1.2 !important;
}

/* 选中状态的上边框 */
.custom-tab-bar .t-tab-bar-item {
  position: relative !important;
}

.custom-tab-bar .active-tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 64rpx;
  height: 6rpx;
  background-color: #2ba471;
  border-radius: 0 0 4rpx 4rpx;
  z-index: 100;
}

/* 超强制选中状态颜色 - 使用!important确保覆盖任何其他样式 */
.custom-tab-bar .t-is-active,
.custom-tab-bar .t-tab-bar-item.t-is-active,
.custom-tab-bar .t-is-active-class,
.custom-tab-bar .active-tab-item {
  color: #2ba471 !important;
  --td-tab-bar-item-tag-active-color: #2ba471 !important;
  --td-tab-bar-item-tag-active-background-color: rgba(43, 164, 113, 0.1) !important;
  font-weight: bold !important;
}

/* 针对theme="tag"模式的特殊处理 */
.custom-tab-bar .t-tab-bar-tag .t-is-active,
.custom-tab-bar .t-tab-bar-tag .t-is-active-class {
  color: #2ba471 !important;
}

/* 专门针对各个tab的处理 */
.custom-tab-bar .tab-item-0.active-tab-item::before,
.custom-tab-bar .tab-item-1.active-tab-item::before,
.custom-tab-bar .tab-item-2.active-tab-item::before,
.custom-tab-bar .tab-item-3.active-tab-item::before {
  content: '' !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: absolute !important;
  top: 0 !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 64rpx !important;
  height: 6rpx !important;
  background-color: #2ba471 !important;
  border-radius: 0 0 4rpx 4rpx !important;
  z-index: 1000 !important;
}

/* 处理图片图标 */
.custom-tab-bar .t-tab-bar-item__image {
  width: 64rpx !important;
  height: 64rpx !important;
}

/* 适配iPhone X等底部有安全区域的机型 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
  .custom-tab-bar {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}


