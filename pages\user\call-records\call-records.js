Page({
  data: {
    callRecords: [],
    loading: true,
    isEmpty: false,
    // 导航栏占位高度
    navPlaceholderHeight: '88px' // 默认值
  },

  onLoad() {
    // 设置导航栏高度占位
    this.setNavPlaceholderHeight();
    this.loadCallRecords();
  },

  onShow() {
    // 检查是否有删除结果需要显示
    if (this.data.deleteResult) {
      const { success, message } = this.data.deleteResult;

      if (success) {
        wx.showToast({
          title: message + '，无效记录已清理',
          icon: 'none',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: message,
          icon: 'none',
          duration: 2000
        });
      }

      // 清除删除结果，避免重复显示
      this.setData({
        deleteResult: null
      });
    }

    // 每次显示页面时刷新数据
    this.loadCallRecords();
  },

  /**
   * 加载拨号记录
   */
  async loadCallRecords() {
    this.setData({ loading: true });

    try {
      // 获取用户openid
      const { result } = await wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'login'
        }
      });

      if (!result.openid) {
        throw new Error('获取用户信息失败');
      }

      const db = wx.cloud.database();

      const res = await db.collection('call_records')
        .where({
          userId: result.openid // 使用真实的openid
        })
        .orderBy('callTime', 'desc')
        .limit(50)
        .get();

      const records = res.data.map(record => ({
        ...record,
        callTimeFormatted: this.formatTime(record.callTime),
        timeAgo: this.getTimeAgo(record.callTime)
      }));

      this.setData({
        callRecords: records,
        loading: false,
        isEmpty: records.length === 0
      });

    } catch (err) {
      console.error('加载拨号记录失败:', err);
      this.setData({
        loading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hour = String(d.getHours()).padStart(2, '0');
    const minute = String(d.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 获取相对时间
   */
  getTimeAgo(date) {
    const now = new Date();
    const callTime = new Date(date);
    const diff = now - callTime;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return this.formatTime(date).split(' ')[0]; // 返回日期部分
  },

  /**
   * 再次拨打电话
   */
  makeCall(e) {
    const { phone } = e.currentTarget.dataset;

    wx.makePhoneCall({
      phoneNumber: phone,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (error) => {
        console.error('拨打电话失败:', error);
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 查看帖子详情
   */
  viewPostDetail(e) {
    const { postId, postType, recordId } = e.currentTarget.dataset;

    if (postType === 'supply') {
      wx.navigateTo({
        url: `/pages/supply/detail/detail?id=${postId}&from=call-records&recordId=${recordId}`
      });
    } else if (postType === 'demand') {
      wx.navigateTo({
        url: `/pages/demand/detail/detail?id=${postId}&from=call-records&recordId=${recordId}`
      });
    }
  },

  /**
   * 删除记录
   */
  deleteRecord(e) {
    const { recordId } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条拨号记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDelete(recordId);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async performDelete(recordId) {
    try {
      // 显示加载提示
      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      // 调用云函数删除记录
      const { result } = await wx.cloud.callFunction({
        name: 'deleteCallRecord',
        data: {
          recordId: recordId
        }
      });

      wx.hideLoading();

      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        // 重新加载数据
        this.loadCallRecords();
      } else {
        wx.showToast({
          title: result.message || '删除失败',
          icon: 'none'
        });
      }

    } catch (err) {
      wx.hideLoading();
      console.error('删除失败:', err);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadCallRecords();
    wx.stopPullDownRefresh();
  },

  /**
   * 设置导航栏占位元素的高度
   */
  setNavPlaceholderHeight() {
    try {
      // 获取系统信息
      const windowInfo = wx.getWindowInfo();

      // 获取状态栏高度
      const statusBarHeight = windowInfo.statusBarHeight || 20;

      // 导航栏高度（一般为44px）
      const navBarHeight = 44;

      // 总高度
      const totalHeight = statusBarHeight + navBarHeight;

      this.setData({
        navPlaceholderHeight: totalHeight + 'px'
      });

    } catch (e) {
      console.error('获取系统信息失败:', e);
      // 使用默认值
      this.setData({
        navPlaceholderHeight: '88px'
      });
    }
  },

  /**
   * 导航栏返回按钮事件
   */
  onNavBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 导航栏首页按钮事件
   */
  onNavHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  }
});