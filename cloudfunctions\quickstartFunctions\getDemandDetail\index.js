// 云函数 - getDemandDetail   渲染求购细节
// 获取求购信息详情

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 主函数
exports.main = async (event, context) => {
  try {
    // 检查参数
    if (!event.id) {
      return {
        code: 1,
        msg: '缺少必要参数id'
      };
    }

    // 获取求购详情
    const result = await db.collection('demand_content')
      .doc(event.id)
      .get();

    // 如果找不到求购信息
    if (!result || !result.data) {
      return {
        code: 1,
        msg: '找不到对应的求购信息'
      };
    }

    // 返回求购数据
    return {
      code: 0,
      data: result.data
    };
  } catch (err) {
    console.error('获取求购详情失败:', err);
    return {
      code: 1,
      msg: '获取求购详情失败',
      err: err
    };
  }
};