// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 获取WX Context (微信调用上下文)，包含 OPENID、APPID 等信息
    const wxContext = cloud.getWXContext()

    // 如果没有传入openid，使用当前调用用户的openid
    const openid = event.openid || wxContext.OPENID

    // 添加调试信息 - 记录输入参数
    // console.log('=== 内容安全检测开始 ===')
    // console.log('输入参数:', {
    //   content: event.content,
    //   contentLength: event.content ? event.content.length : 0,
    //   version: event.version || 2,
    //   scene: event.scene || 2,
    //   openid: openid,
    //   title: event.title || '',
    //   timestamp: new Date().toISOString()
    // })

    // 检查必要参数
    if (!event.content) {
      // console.log('检测失败: 内容为空')
      return {
        errcode: 87014,
        errmsg: '内容为空'
      }
    }

    // 检查内容长度
    if (event.content.length > 5000) {
      // console.log('检测失败: 内容过长', event.content.length)
      return {
        errcode: 87014,
        errmsg: '内容过长，超过5000字符限制'
      }
    }

    // 调用微信内容安全检测API
    // console.log('开始调用微信内容安全检测API...')
    const result = await cloud.openapi.security.msgSecCheck({
      content: event.content,
      version: event.version || 2,
      scene: event.scene || 2,
      openid: openid,
      title: event.title || ''
    })

    // 添加调试信息 - 记录检测结果
    // console.log('微信API检测结果:', {
    //   errCode: result.errCode,
    //   errMsg: result.errMsg,
    //   suggest: result.result ? result.result.suggest : 'unknown',
    //   label: result.result ? result.result.label : 'unknown',
    //   traceId: result.traceId,
    //   timestamp: new Date().toISOString()
    // })

    // 如果有详细信息，也记录下来
    // if (result.detail && result.detail.length > 0) {
    //   console.log('检测详细信息:', result.detail)
    // }

    // console.log('=== 内容安全检测结束 ===')

    // 返回检测结果
    return result
  } catch (err) {
    // 如果API调用失败，返回错误信息
    // const wxContext = cloud.getWXContext()
    // console.error('=== 内容安全检测异常 ===')
    // console.error('错误详情:', {
    //   message: err.message,
    //   errcode: err.errcode,
    //   errmsg: err.errmsg,
    //   stack: err.stack,
    //   timestamp: new Date().toISOString()
    // })
    // console.error('输入参数:', {
    //   content: event.content,
    //   contentLength: event.content ? event.content.length : 0,
    //   openid: event.openid || wxContext.OPENID,
    //   scene: event.scene,
    //   version: event.version,
    //   title: event.title
    // })
    console.error('内容安全检测失败:', err)

    return {
      errcode: err.errcode || -1,
      errmsg: err.errmsg || '内容安全检测服务异常'
    }
  }
}
