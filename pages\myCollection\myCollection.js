// pages/myCollection/myCollection.js
const collectionUtils = require('../../utils/collectionUtils.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 当前选中的标签页 0-供应收藏 1-求购收藏
    activeTab: 0,
    // 供应收藏列表
    supplyCollections: [],
    // 求购收藏列表
    demandCollections: [],
    // 加载状态
    loading: false,
    // 是否还有更多数据
    hasMore: true,
    // 当前页码
    page: 1,
    // 每页数量
    pageSize: 10,
    // 用户登录状态
    isLogined: false,
    // 用户ID
    userId: null,
    // 导航栏占位高度
    navPlaceholderHeight: '88px', // 默认值
    navHeight: '',
    statusBarHeight: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置导航栏高度占位
    this.setNavPlaceholderHeight();

    // 检查登录状态
    this.checkLoginStatus();

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '我的收藏'
    });

    // 监听屏幕旋转或尺寸变化事件
    var self = this;
    wx.onWindowResize(function() {
      // 重新计算导航栏高度
      self.setNavPlaceholderHeight();
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新检查登录状态，但只在状态变化时加载数据
    this.checkLoginStatusAndRefreshIfNeeded();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的收藏夹',
      path: '/pages/myCollection/myCollection'
    };
  },

  /**
   * 设置导航栏占位元素的高度 - 简化版
   */
  setNavPlaceholderHeight() {
    try {
      // 获取系统信息
      const windowInfo = wx.getWindowInfo();
      const deviceInfo = wx.getDeviceInfo() || wx.getSystemInfoSync();

      // 获取状态栏高度
      const statusBarHeight = windowInfo.statusBarHeight || 20;

      // 获取胶囊按钮信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

      // 计算导航栏高度
      const navBarHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;

      // 总高度 = 状态栏高度 + 导航栏高度
      const totalHeight = statusBarHeight + navBarHeight;

      

      this.setData({
        navPlaceholderHeight: totalHeight + 'px',
        navHeight: navBarHeight + 'px',
        statusBarHeight: statusBarHeight + 'px'
      });

    } catch (e) {
      console.error('获取系统信息失败:', e);
      // 使用默认值
      this.setData({
        navPlaceholderHeight: '88px',
        navHeight: '44px',
        statusBarHeight: '20px'
      });
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp();
    const isLogined = app.globalData.isLogined;
    const userId = app.globalData.userId;

    this.setData({
      isLogined: isLogined,
      userId: userId
    });

    if (isLogined && userId) {
      // 如果已登录，加载收藏数据
      this.loadCollectionData();
    }
  },

  /**
   * 标签页切换
   */
  onTabChange(e) {
    const activeTab = e.detail.value;
    this.setData({
      activeTab: activeTab,
      page: 1,
      hasMore: true
    });

    // 重新加载对应标签页的数据
    this.loadCollectionData();
  },

  /**
   * 检查登录状态并在需要时刷新数据
   */
  checkLoginStatusAndRefreshIfNeeded() {
    const app = getApp();
    const isLogined = app.globalData.isLogined;
    const userId = app.globalData.userId;

    // 检查登录状态是否发生变化
    const previousIsLogined = this.data.isLogined;
    const previousUserId = this.data.userId;

    this.setData({
      isLogined: isLogined,
      userId: userId
    });

    // 只在以下情况重新加载数据：
    // 1. 从未登录变为已登录
    // 2. 用户ID发生变化（切换用户）
    if (isLogined && userId &&
        (!previousIsLogined || previousUserId !== userId)) {
      console.log('登录状态变化，重新加载收藏数据');
      this.loadCollectionData();
    }
  },

  /**
   * 加载收藏数据
   */
  loadCollectionData() {
    if (!this.data.isLogined || !this.data.userId) {
      return;
    }

    this.setData({ loading: true });

    // 根据当前标签页加载不同类型的收藏
    if (this.data.activeTab === 0) {
      this.loadSupplyCollections();
    } else {
      this.loadDemandCollections();
    }
  },

  /**
   * 加载供应收藏
   */
  loadSupplyCollections: function() {
    var self = this;
    if (!this.data.isLogined || !this.data.userId) {
      this.setData({ loading: false });
      return;
    }

    collectionUtils.getCollectionList('supply', this.data.page, this.data.pageSize).then(function(result) {
      var formattedData = result.data.map(function(item) {
        var formattedItem = collectionUtils.formatCollectionItem(item);
        formattedItem.type = 'supply';
        // 如果收藏记录中的图片为空，尝试从原始帖子获取
        if (!formattedItem.imageUrl && item.originalItem) {
          formattedItem.imageUrl = self.getFirstImageUrl(item.originalItem);
        }
        // 添加参数信息，使用原始帖子数据
        formattedItem.params = self.formatSupplyParams(item.originalItem || item);
        return formattedItem;
      });

      // 如果是第一页，替换数据；否则追加数据
      var currentList = self.data.page === 1 ? [] : self.data.supplyCollections;
      var newList = currentList.concat(formattedData);

      self.setData({
        supplyCollections: newList,
        hasMore: result.hasMore,
        loading: false
      });
    }).catch(function(error) {
      console.error('加载供应收藏失败:', error);
      self.setData({ loading: false });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 加载求购收藏
   */
  loadDemandCollections: function() {
    var self = this;
    if (!this.data.isLogined || !this.data.userId) {
      this.setData({ loading: false });
      return;
    }

    collectionUtils.getCollectionList('demand', this.data.page, this.data.pageSize).then(function(result) {
      var formattedData = result.data.map(function(item) {
        var formattedItem = collectionUtils.formatCollectionItem(item);
        formattedItem.type = 'demand';
        // 如果收藏记录中的图片为空，尝试从原始帖子获取
        if (!formattedItem.imageUrl && item.originalItem) {
          formattedItem.imageUrl = self.getFirstImageUrl(item.originalItem);
        }
        // 添加参数信息，使用原始帖子数据
        formattedItem.params = self.formatDemandParams(item.originalItem || item);
        return formattedItem;
      });

      // 如果是第一页，替换数据；否则追加数据
      var currentList = self.data.page === 1 ? [] : self.data.demandCollections;
      var newList = currentList.concat(formattedData);

      self.setData({
        demandCollections: newList,
        hasMore: result.hasMore,
        loading: false
      });
    }).catch(function(error) {
      console.error('加载求购收藏失败:', error);
      self.setData({ loading: false });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 同步收藏缓存数据
   */
  syncCollectionCache: function() {
    if (!this.data.isLogined || !this.data.userId) {
      return;
    }

    console.log('开始同步收藏缓存数据');

    // 调用云函数同步缓存
    wx.cloud.callFunction({
      name: 'collectionManager',
      data: {
        action: 'syncCache'
      }
    }).then(function(res) {
      if (res.result && res.result.success) {
        console.log('收藏缓存同步完成，更新了', res.result.updatedCount, '条记录');
      }
    }).catch(function(error) {
      console.error('同步收藏缓存失败:', error);
    });
  },

  /**
   * 获取第一张图片URL
   */
  getFirstImageUrl: function(item) {
    if (!item) return '';

    // 1. 优先检查 newImagesList（新类型图片数组）
    if (item.newImagesList && Array.isArray(item.newImagesList) && item.newImagesList.length > 0) {
      var firstImage = item.newImagesList[0];
      // 如果是对象，取url属性；如果是字符串，直接返回
      return typeof firstImage === 'object' && firstImage.url ? firstImage.url : firstImage;
    }

    // 2. 检查 imageList（旧类型图片数组）
    if (item.imageList && Array.isArray(item.imageList) && item.imageList.length > 0) {
      var firstImage = item.imageList[0];
      // 如果是对象，取url属性；如果是字符串，直接返回
      return typeof firstImage === 'object' && firstImage.url ? firstImage.url : firstImage;
    }

    // 3. 检查 newImageList（可能的命名变体）
    if (item.newImageList && Array.isArray(item.newImageList) && item.newImageList.length > 0) {
      var firstImage = item.newImageList[0];
      return typeof firstImage === 'object' && firstImage.url ? firstImage.url : firstImage;
    }

    // 如果都没有，返回空字符串
    return '';
  },

  /**
   * 格式化供应参数
   */
  formatSupplyParams: function(item) {
    var params = [];

    // 供应参数：米经，胸径，地径，杯口，高度，冠幅，分支，丛生，杆径
    if (item.meter_diameter) {
      params.push({ name: '米经', value: item.meter_diameter + 'cm' });
    }
    if (item.thorax_diameter) {
      params.push({ name: '胸径', value: item.thorax_diameter + 'cm' });
    }
    if (item.ground_diameter) {
      params.push({ name: '地径', value: item.ground_diameter + 'cm' });
    }
    if (item.cup) {
      params.push({ name: '杯口', value: item.cup + 'cm' });
    }
    if (item.height) {
      params.push({ name: '高度', value: item.height + 'cm' });
    }
    if (item.canopy) {
      params.push({ name: '冠幅', value: item.canopy + 'cm' });
    }
    if (item.branch_count) {
      params.push({ name: '分支', value: item.branch_count + '个' });
    }
    if (item.clumpCount) {
      params.push({ name: '丛生', value: item.clumpCount + '杆' });
    }
    if (item.clumpDiameter) {
      params.push({ name: '杆径', value: item.clumpDiameter + 'cm' });
    }

    return params;
  },

  /**
   * 格式化求购参数
   */
  formatDemandParams: function(item) {
    var params = [];

    // 求购参数：米经，地径，高度，冠幅，杯口，丛生
    if (item.meter_diameter) {
      params.push({ name: '米经', value: item.meter_diameter + 'cm' });
    }
    if (item.ground_diameter) {
      params.push({ name: '地径', value: item.ground_diameter + 'cm' });
    }
    if (item.height) {
      params.push({ name: '高度', value: item.height + 'cm' });
    }
    if (item.canopy) {
      params.push({ name: '冠幅', value: item.canopy + 'cm' });
    }
    if (item.cup) {
      params.push({ name: '杯口', value: item.cup + 'cm' });
    }
    if (item.clumpCount) {
      params.push({ name: '丛生', value: item.clumpCount + '杆' });
    }

    return params;
  },

  /**
   * 刷新数据
   */
  refreshData() {
    // 根据当前活跃的tab，只清空对应的数据数组
    const updateData = {
      page: 1,
      hasMore: true
    };

    // 只清空当前活跃tab的数据，保留其他tab的数据
    if (this.data.activeTab === 0) {
      // 当前在供应收藏tab，只清空供应收藏数据
      updateData.supplyCollections = [];
    } else {
      // 当前在求购收藏tab，只清空求购收藏数据
      updateData.demandCollections = [];
    }

    this.setData(updateData);

    this.loadCollectionData();

    // 停止下拉刷新
    setTimeout(function() {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    const nextPage = this.data.page + 1;
    this.setData({
      page: nextPage,
      loading: true
    });

    // 根据当前标签页加载更多数据
    if (this.data.activeTab === 0) {
      this.loadSupplyCollections();
    } else {
      this.loadDemandCollections();
    }
  },

  /**
   * 导航栏返回按钮事件
   */
  onNavBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 导航栏首页按钮事件
   */
  onNavHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  /**
   * 跳转到登录页面
   */
  goToLogin() {
    wx.switchTab({
      url: '/pages/user/user'
    });
  },

  /**
   * 跳转到供应页面
   */
  goToSupplyPage() {
    wx.switchTab({
      url: '/pages/supply/supply'
    });
  },

  /**
   * 跳转到求购页面
   */
  goToDemandPage() {
    wx.switchTab({
      url: '/pages/demand/demand'
    });
  },

  /**
   * 收藏项目点击事件
   */
  onCollectionItemTap: function(e) {
    const { id, type, index } = e.currentTarget.dataset;
    const self = this;

    // 先检查帖子是否还存在
    this.checkPostExists(id, type).then(function(exists) {
      if (exists) {
        // 帖子存在，正常跳转
        if (type === 'supply') {
          wx.navigateTo({
            url: '/pages/supply/detail/detail?id=' + id
          });
        } else if (type === 'demand') {
          wx.navigateTo({
            url: '/pages/demand/detail/detail?id=' + id
          });
        }
      } else {
        // 帖子已被删除，提示用户并移除收藏
        wx.showModal({
          title: '提示',
          content: '该帖子已被删除，将自动移除此收藏',
          showCancel: false,
          confirmText: '确定',
          success: function() {
            // 自动删除该收藏项
            self.removeInvalidCollection(id, type, index);
          }
        });
      }
    }).catch(function(error) {
      console.error('检查帖子存在性失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 检查帖子是否还存在
   */
  checkPostExists: function(postId, postType) {
    return new Promise(function(resolve, reject) {
      const db = wx.cloud.database();
      const collectionName = postType === 'supply' ? 'supply_content' : 'demand_content';

      db.collection(collectionName).doc(postId).get().then(function(res) {
        // 检查帖子是否存在
        if (res.data) {
          resolve(true);
        } else {
          console.log('帖子不存在:', postId);
          resolve(false);
        }
      }).catch(function(error) {
        console.error('检查帖子存在性失败:', error);
        // 如果查询失败，假设帖子不存在
        resolve(false);
      });
    });
  },

  /**
   * 移除无效收藏
   */
  removeInvalidCollection: function(postId, postType, index) {
    const self = this;

    // 调用删除收藏接口
    collectionUtils.removeCollection(postId, postType).then(function(result) {
      if (result.success) {
        // 从本地数据中移除
        if (postType === 'supply') {
          var newList = self.data.supplyCollections.slice();
          newList.splice(index, 1);
          self.setData({
            supplyCollections: newList
          });
        } else {
          var newList = self.data.demandCollections.slice();
          newList.splice(index, 1);
          self.setData({
            demandCollections: newList
          });
        }

        wx.showToast({
          title: '已移除无效收藏',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '移除失败，请重试',
          icon: 'none'
        });
      }
    }).catch(function(error) {
      console.error('移除无效收藏失败:', error);
      wx.showToast({
        title: '移除失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 删除收藏项目
   */
  onDeleteCollection: function(e) {
    const { id, type, index } = e.currentTarget.dataset;
    const self = this;

    // 使用 catchtap 已经阻止了事件冒泡，无需手动调用 stopPropagation

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个收藏吗？',
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: function(result) {
        if (!result.confirm) {
          return;
        }

        // 调用删除收藏接口
        collectionUtils.removeCollection(id, type).then(function(deleteResult) {
          if (deleteResult.success) {
            // 从本地数据中移除
            if (type === 'supply') {
              var newList = self.data.supplyCollections.slice();
              newList.splice(index, 1);
              self.setData({
                supplyCollections: newList
              });
            } else {
              var newList = self.data.demandCollections.slice();
              newList.splice(index, 1);
              self.setData({
                demandCollections: newList
              });
            }
          }
        }).catch(function(error) {
          console.error('删除收藏失败:', error);
          wx.showToast({
            title: '删除失败，请重试',
            icon: 'none'
          });
        });
      }
    });
  }
});
