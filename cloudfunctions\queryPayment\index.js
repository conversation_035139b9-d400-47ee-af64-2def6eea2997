// 云函数 - queryPayment (查询支付订单状态)
const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 配置信息
const config = {
  appid: 'your-appid',
  mchid: 'your-mchid', 
  serial_no: 'your-cert-serial-no',
  apiv3_private_key: 'your-apiv3-key',
  private_key: `-----BEGIN PRIVATE KEY-----
your-private-key-content-here
-----END PRIVATE KEY-----`
}

/**
 * 生成随机字符串
 */
function generateNonceStr() {
  return Math.random().toString(36).substring(2, 17)
}

/**
 * 生成签名
 */
function generateSignature(method, url, timestamp, nonce_str, body) {
  const message = `${method}\n${url}\n${timestamp}\n${nonce_str}\n${body}\n`
  const privateKey = config.private_key
  
  const sign = crypto.createSign('RSA-SHA256')
  sign.update(message)
  return sign.sign(privateKey, 'base64')
}

/**
 * 构建Authorization头
 */
function buildAuthorizationHeader(method, url, body) {
  const timestamp = Math.floor(Date.now() / 1000)
  const nonce_str = generateNonceStr()
  const signature = generateSignature(method, url, timestamp, nonce_str, body)
  
  return `WECHATPAY2-SHA256-RSA2048 mchid="${config.mchid}",nonce_str="${nonce_str}",signature="${signature}",timestamp="${timestamp}",serial_no="${config.serial_no}"`
}

/**
 * 查询支付订单
 */
async function queryPaymentOrder(out_trade_no) {
  const url = `/v3/pay/transactions/out-trade-no/${out_trade_no}?mchid=${config.mchid}`
  const method = 'GET'
  const body = ''
  
  const authorization = buildAuthorizationHeader(method, url, body)
  
  return new Promise((resolve, reject) => {
    const https = require('https')
    const options = {
      hostname: 'api.mch.weixin.qq.com',
      port: 443,
      path: url,
      method: method,
      headers: {
        'Accept': 'application/json',
        'Authorization': authorization
      }
    }
    
    const req = https.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data)
          if (res.statusCode === 200) {
            resolve(result)
          } else {
            reject(new Error(`查询失败: ${data}`))
          }
        } catch (error) {
          reject(new Error(`解析响应失败: ${error.message}`))
        }
      })
    })
    
    req.on('error', (error) => {
      reject(error)
    })
    
    req.end()
  })
}

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const { out_trade_no } = event
    
    if (!out_trade_no) {
      throw new Error('订单号不能为空')
    }
    
    // 查询微信支付订单状态
    const paymentInfo = await queryPaymentOrder(out_trade_no)
    
    return {
      success: true,
      data: {
        out_trade_no: paymentInfo.out_trade_no,
        transaction_id: paymentInfo.transaction_id,
        trade_state: paymentInfo.trade_state,
        trade_state_desc: paymentInfo.trade_state_desc,
        amount: paymentInfo.amount,
        success_time: paymentInfo.success_time
      }
    }
    
  } catch (error) {
    console.error('查询支付订单失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
