<!-- pages/myCollection/myCollection.wxml -->
<view class="page-container">
  <!-- 导航栏组件 -->
  <view class="nav-container">
    <navigation-bar title="我的收藏" back="{{true}}" home="{{true}}" bind:back="onNavBack" bind:home="onNavHome" extClass="custom-nav"></navigation-bar>
  </view>

  <!-- 导航栏占位元素 - 动态计算高度 -->
  <view class="nav-placeholder" style="height: {{navPlaceholderHeight}}"></view>

  <!-- 主内容区 -->
  <view class="content-container">
    <!-- 未登录状态 -->
    <view class="empty-state" wx:if="{{!isLogined}}">
      <t-icon name="info-circle" size="80rpx" color="#43a047"></t-icon>
      <text class="empty-text">请先登录后查看您的收藏</text>
      <view class="login-btn" bindtap="goToLogin">
        <t-button theme="primary" size="large" block>去登录</t-button>
      </view>
    </view>

    <!-- 已登录状态 -->
    <view class="logged-in-content" wx:if="{{isLogined}}">
      <!-- 标签页切换 -->
      <view class="tabs-container">
        <t-tabs value="{{activeTab}}" bind:change="onTabChange" theme="line" placement="top">
          <t-tab-panel label="供应收藏" value="0">
            <!-- 供应收藏列表 -->
            <view class="collection-list">
              <!-- 加载状态 -->
              <view class="loading-container" style="--td-loading-color : #43a047" wx:if="{{loading}}">
                <t-loading theme="circular" size="40rpx" text="加载中..."></t-loading>
              </view>

              <!-- 空状态 -->
              <view class="empty-collection" wx:if="{{!loading && supplyCollections.length === 0}}">
                <t-icon name="heart" size="120rpx" color="#ddd"></t-icon>
                <text class="empty-text">暂无供应收藏</text>
                <text class="empty-desc">去供应页面收藏您感兴趣的内容吧</text>
              </view>

              <!-- 供应收藏项目列表 -->
              <view class="collection-item supply-item"
                    wx:for="{{supplyCollections}}" wx:key="id"
                    bindtap="onCollectionItemTap" data-id="{{item.id}}" data-type="{{item.type}}" data-index="{{index}}">
                <!-- 收藏项目内容 -->
                <view class="item-content {{!item.imageUrl ? 'no-image-content' : ''}}">
                  <!-- 左侧图片 -->
                  <view class="item-image">
                    <image src="{{item.imageUrl || ''}}" mode="aspectFill" />
                  </view>

                  <!-- 中间信息 -->
                  <view class="item-info">
                    <!-- 标题和价格在同一行 -->
                    <view class="item-header">
                      <view class="item-title">{{item.title}}</view>
                      <view class="item-price" wx:if="{{item.formattedPrice}}">{{item.formattedPrice}}</view>
                    </view>

                    <!-- 参数信息 -->
                    <view class="item-params" wx:if="{{item.params && item.params.length > 0}}">
                      <view class="param-item" wx:for="{{item.params}}" wx:key="name" wx:for-item="param">
                        <text class="param-name">{{param.name}}:</text>
                        <text class="param-value">{{param.value}}</text>
                      </view>
                    </view>

                    <!-- 简介内容 -->
                    <view class="item-description" wx:if="{{item.content}}">{{item.content}}</view>

                    <!-- 底部信息：左侧收藏时间，右侧地址 -->
                    <view class="item-bottom">
                      <view class="item-bottom-left">收藏于 {{item.formattedTime}}</view>
                      <view class="item-bottom-right" wx:if="{{item.address}}">{{item.address}}</view>
                    </view>
                  </view>

                  <!-- 右侧删除按钮 -->
                  <view class="item-actions">
                    <view class="delete-btn" catchtap="onDeleteCollection"
                          data-id="{{item.id}}" data-type="{{item.type}}" data-index="{{index}}">
                      <t-icon name="delete" size="41rpx" color="#ff4444" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </t-tab-panel>

          <t-tab-panel label="求购收藏" value="1">
            <!-- 求购收藏列表 -->
            <view class="collection-list">
              <!-- 加载状态 -->
              <view class="loading-container" style="--td-loading-color : #43a047" wx:if="{{loading}}">
                <t-loading theme="circular" size="40rpx" text="加载中..."></t-loading>
              </view>

              <!-- 空状态 -->
              <view class="empty-collection" wx:if="{{!loading && demandCollections.length === 0}}">
                <t-icon name="heart" size="120rpx" color="#ddd"></t-icon>
                <text class="empty-text">暂无求购收藏</text>
                <text class="empty-desc">去求购页面收藏您感兴趣的内容吧</text>
              </view>

              <!-- 求购收藏项目列表 -->
              <view class="collection-item demand-item"
                    wx:for="{{demandCollections}}" wx:key="id"
                    bindtap="onCollectionItemTap" data-id="{{item.id}}" data-type="{{item.type}}" data-index="{{index}}">
                <!-- 收藏项目内容 -->
                <view class="item-content {{!item.imageUrl ? 'no-image-content' : ''}}">
                  <!-- 左侧图片 -->
                  <view class="item-image">
                    <image src="{{item.imageUrl || ''}}" mode="aspectFill" />
                  </view>

                  <!-- 中间信息 -->
                  <view class="item-info">
                    <!-- 标题和价格在同一行 -->
                    <view class="item-header">
                      <view class="item-title demand-title">【求购】{{item.title}}</view>
                      <view class="item-price" wx:if="{{item.formattedPrice}}">{{item.formattedPrice}}</view>
                    </view>

                    <!-- 参数信息 -->
                    <view class="item-params" wx:if="{{item.params && item.params.length > 0}}">
                      <view class="param-item" wx:for="{{item.params}}" wx:key="name" wx:for-item="param">
                        <text class="param-name">{{param.name}}:</text>
                        <text class="param-value">{{param.value}}</text>
                      </view>
                    </view>

                    <!-- 简介内容 -->
                    <view class="item-description" wx:if="{{item.content}}">{{item.content}}</view>

                    <!-- 底部信息：左侧收藏时间，右侧地址 -->
                    <view class="item-bottom">
                      <view class="item-bottom-left">收藏于 {{item.formattedTime}}</view>
                      <view class="item-bottom-right" wx:if="{{item.address}}">{{item.address}}</view>
                    </view>
                  </view>

                  <!-- 右侧删除按钮 -->
                  <view class="item-actions">
                    <view class="delete-btn" catchtap="onDeleteCollection"
                          data-id="{{item.id}}" data-type="{{item.type}}" data-index="{{index}}">
                      <t-icon name="delete" size="41rpx" color="#ff4444" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </t-tab-panel>
        </t-tabs>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="bottom-safe-area"></view>
</view>
