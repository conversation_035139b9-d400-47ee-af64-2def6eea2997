// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { 
      postId, 
      postType, 
      postTitle, 
      phoneNumber 
    } = event

    // 参数验证
    if (!postId || !postType || !postTitle || !phoneNumber) {
      return {
        success: false,
        message: '缺少必要参数'
      }
    }

    // 验证postType
    if (!['supply', 'demand'].includes(postType)) {
      return {
        success: false,
        message: '无效的帖子类型'
      }
    }

    // 创建拨号记录
    const callRecord = {
      userId: wxContext.OPENID, // 使用用户的openid
      postId: postId,
      postType: postType,
      postTitle: postTitle,
      phoneNumber: phoneNumber,
      callTime: db.serverDate(),
      createTime: db.serverDate()
    }

    // 保存到数据库
    const result = await db.collection('call_records').add({
      data: callRecord
    })

    return {
      success: true,
      message: '拨号记录保存成功',
      recordId: result._id
    }

  } catch (error) {
    console.error('保存拨号记录失败:', error)
    return {
      success: false,
      message: '保存拨号记录失败',
      error: error.message
    }
  }
}
