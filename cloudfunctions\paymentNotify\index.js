// 云函数 - paymentNotify
const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 配置信息
const config = {
  apiv3_key: '15700526449td482471813td48247181' // APIv3密钥
}

/**
 * 解密微信支付回调数据
 */
function decryptData(ciphertext, associated_data, nonce) {
  try {
    const key = Buffer.from(config.apiv3_key, 'utf8')
    const iv = Buffer.from(nonce, 'utf8')
    const encrypted = Buffer.from(ciphertext, 'base64')
    const authTag = encrypted.slice(-16)
    const data = encrypted.slice(0, -16)

    const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv)
    decipher.setAuthTag(authTag)
    decipher.setAAD(Buffer.from(associated_data, 'utf8'))

    let decrypted = decipher.update(data, null, 'utf8')
    decrypted += decipher.final('utf8')

    return JSON.parse(decrypted)
  } catch (error) {
    console.error('解密失败:', error)
    throw new Error('解密失败')
  }
}

/**
 * 验证签名
 */
function verifySignature(timestamp, nonce, body, signature) {
  // 这里应该使用微信支付平台证书验证签名
  // 简化处理，实际项目中需要下载并验证平台证书
  return true
}

/**
 * 处理支付成功
 */
async function handlePaymentSuccess(orderData) {
  const { out_trade_no, transaction_id, payer, amount } = orderData

  try {
    // 从订单号中解析积分数量
    // 金额单位是分，需要转换为元，然后按比例转换为积分
    const amountInYuan = amount.total / 100; // 分转元
    // 特殊处理：0.01元=1积分，其他按1元=1积分
    const points = amountInYuan === 0.01 ? 1 : Math.floor(amountInYuan);
    const openid = payer.openid

    console.log(`处理支付成功: 订单${out_trade_no}, 用户${openid}, 金额${amountInYuan}元, 积分${points}`)

    // 使用订单号作为唯一标识，防止重复处理
    // 先尝试创建积分日志记录，如果已存在则说明已处理过
    try {
      await db.collection('point_logs').add({
        data: {
          order_no: out_trade_no,
          openid: openid,
          type: 'recharge',
          amount: points,
          description: `充值获得${points}积分`,
          transaction_id: '', // 稍后更新
          create_time: new Date(),
          status: 'processing' // 处理中状态
        }
      });
      console.log(`创建积分日志记录成功: ${out_trade_no}`);
    } catch (error) {
      // 如果添加失败，可能是重复订单号，检查是否已处理
      const existingLog = await db.collection('point_logs')
        .where({
          order_no: out_trade_no,
          type: 'recharge'
        })
        .get();

      if (existingLog.data.length > 0) {
        console.log(`订单${out_trade_no}已处理过，跳过重复处理`);
        return true;
      }

      // 其他错误，重新抛出
      console.error('创建积分日志失败:', error);
      throw error;
    }

    // 更新用户积分 - 注意：云函数中查询用户时不能用_openid字段
    console.log(`尝试更新用户积分，查询条件:`, { openid });

    // 先查询用户
    const userResult = await db.collection('users')
      .where({
        _openid: openid
      })
      .get();

    //console.log(`用户查询结果:`, userResult);

    if (userResult.data.length === 0) {
      console.warn(`用户${openid}不存在，无法更新积分`);
      // 不抛出错误，避免微信重复回调
      return false;
    }

    // 更新用户积分
    const updateResult = await db.collection('users')
      .doc(userResult.data[0]._id)  // 使用文档ID更新
      .update({
        data: {
          reward: db.command.inc(points)
        }
      });

    if (updateResult.stats.updated === 0) {
      console.warn(`用户积分更新失败`);
      // 不抛出错误，避免微信重复回调
      return false;
    }

    // 更新积分日志状态为完成，并添加交易ID
    await db.collection('point_logs')
      .where({
        order_no: out_trade_no,
        type: 'recharge',
        status: 'processing'
      })
      .update({
        data: {
          transaction_id: transaction_id,
          status: 'completed',
          complete_time: new Date()
        }
      })

    console.log(`支付成功处理完成: ${out_trade_no}, 用户${openid}获得${points}积分`)
    return true

  } catch (error) {
    console.error('处理支付成功失败:', error)
    throw error
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 兼容HTTP触发器和直接调用
    const headers = event.headers || event.Headers || {};
    const body = event.body || event.Body || JSON.stringify(event);

    console.log('paymentNotify 被调用，event:', JSON.stringify(event));
    console.log('Node.js 版本:', process.version);
    console.log('APIv3密钥长度:', config.apiv3_key.length);
    
    // 验证签名
    const signature = headers['wechatpay-signature']
    const timestamp = headers['wechatpay-timestamp']
    const nonce = headers['wechatpay-nonce']
    
    if (!verifySignature(timestamp, nonce, body, signature)) {
      return {
        statusCode: 401,
        body: JSON.stringify({ code: 'FAIL', message: '签名验证失败' })
      }
    }
    
    // 解析回调数据
    const callbackData = JSON.parse(body)
    
    if (callbackData.event_type === 'TRANSACTION.SUCCESS') {
      // 解密支付数据
      const resource = callbackData.resource
      const orderData = decryptData(
        resource.ciphertext,
        resource.associated_data,
        resource.nonce
      )
      
      // 处理支付成功
      await handlePaymentSuccess(orderData)
      
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: 'SUCCESS', message: '处理成功' })
      }
    }

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: 'SUCCESS', message: '忽略事件' })
    }

  } catch (error) {
    console.error('支付回调处理失败:', error)
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: 'FAIL', message: error.message })
    }
  }
}
