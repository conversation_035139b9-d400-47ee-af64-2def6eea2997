/* pages/myCollection/myCollection.wxss */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 0;
}

/* 导航栏容器 */
.nav-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #fff;
}

/* 导航栏占位元素 - 优化版 */
.nav-placeholder {
  width: 100%;
  background: #43a047;
  position: relative;
}

/* 自定义导航栏样式 */
.custom-nav {
  background: #43a047 !important;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 999 !important;
  position: relative !important;
  width: 100% !important;
}

.custom-nav::after {
  display: none;
}

.custom-nav .weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.custom-nav .weui-navigation-bar__center {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  font-weight: bold;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper {
  transition: all 0.3s ease;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper.weui-active {
  opacity: 0.7;
  transform: scale(0.95);
}

/* 主内容区 */
.content-container {
  padding-top: 0; /* 移除固定的padding-top，使用动态占位元素 */
  min-height: 100vh;
}

/* 未登录状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-state .empty-text {
  font-size: 32rpx;
  color: #666;
  margin: 40rpx 0;
}

.empty-state .login-btn {
  width: 300rpx;
  margin-top: 40rpx;
}

/* 已登录内容区 */
.logged-in-content {
  padding: 0;
}

/* 标签页容器 */
.tabs-container {
  background-color: #fff;
  --td-tab-item-active-color:#14aa06;
  --td-tab-track-color:#14aa06;
  margin: 0;
}

/* 收藏列表 */
.collection-list {
  min-height: 400rpx;
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

/* 空收藏状态 */
.empty-collection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-collection .empty-text {
  font-size: 32rpx;
  color: #666;
  margin: 40rpx 0 20rpx 0;
}

.empty-collection .empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}


/* 收藏项目 */
.collection-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.collection-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.12);
}

/* 供应类型左边框 */
.collection-item.supply-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #43a047, #66bb6a);
}

/* 求购类型左边框 */
.collection-item.demand-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #43a047, #66bb6a);
}

/* 收藏项目内容 */
.item-content {
  display: flex;
  align-items: flex-start;
  padding: 26rpx;
  min-height: 175rpx;
  position: relative;
}

/* 左侧图片 */
.item-image {
  width: 105rpx;
  height: 105rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  margin-right: 21rpx;
  background-color: #f5f5f5;
}

.item-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 中间信息区域 */
.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 105rpx;
  padding-right: 11rpx;
  padding-bottom: 31rpx;
}

/* 无图片时的样式 */
.item-content.no-image-content .item-image {
  display: none;
}

.item-content.no-image-content .item-info {
  margin-left: 0;
  padding-left: 0;
}

/* 标题和价格容器 */
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
  gap: 10rpx;
}

/* 标题 */
.item-title {
  font-size: 31rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

/* 求购类型标题样式 */
.item-title.demand-title {
  color: #43a047;
}

/* 价格 */
.item-price {
  font-size: 27rpx;
  color: #ff6b35;
  font-weight: 600;
  line-height: 1.2;
  flex-shrink: 0;
  text-align: right;
}

/* 参数信息容器 */
.item-params {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 14rpx;
}

/* 单个参数项 */
.param-item {
  display: flex;
  align-items: center;
  background-color: #f0f8f0;
  border-radius: 4rpx;
  padding: 4rpx 9rpx;
  font-size: 23rpx;
  line-height: 1.2;
}

/* 参数名称 */
.param-name {
  color: #43a047;
  font-weight: 500;
  margin-right: 4rpx;
}

/* 参数值 */
.param-value {
  color: #666;
}

/* 简介内容 */
.item-description {
  font-size: 25rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 13rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  background-color: #f8f9fa;
  padding: 9rpx 13rpx;
  border-radius: 6rpx;
  border-left: 3rpx solid #43a047;
}

/* 地址 */
.item-address {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 收藏时间 - 相对于 item-content 定位 */
.item-time {
  font-size: 20rpx;
  color: #999;
  line-height: 1.2;
  position: absolute;
  bottom: 24rpx;
  left: 24rpx;
}

/* 底部信息容器 - 相对于 item-content 定位 */
.item-bottom {
  position: absolute;
  bottom: 24rpx;
  left: 24rpx;
  right: 45rpx; /* 进一步减少右边距，让地址更靠右 */
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

/* 底部左侧（收藏时间） */
.item-bottom-left {
  font-size: 21rpx;
  color: #999;
  line-height: 1.2;
}

/* 底部右侧（地址） */
.item-bottom-right {
  font-size: 23rpx;
  color: #666;
  line-height: 1.2;
  max-width: 200rpx;
  text-align: right;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右侧操作区域 */
.item-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 10rpx;
  flex-shrink: 0;
}

/* 删除按钮 */
.delete-btn {
  width: 58rpx;
  height: 58rpx;
  border-radius: 50%;
  background-color: #fff;
  border: 2rpx solid #ff4444;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 6rpx rgba(255, 68, 68, 0.2);
}

.delete-btn:active {
  background-color: #ff4444;
  transform: scale(0.9);
  box-shadow: 0 1rpx 3rpx rgba(255, 68, 68, 0.3);
}

.delete-btn:active .t-icon {
  color: #fff !important;
}

/* 底部安全区域 */
.bottom-safe-area {
  height: 120rpx;
  background-color: transparent;
}

/* TDesign 组件样式覆盖 */
.t-tabs {
  background-color: #fff;
}

.t-tabs__header {
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.t-tabs__item {
  font-size: 30rpx;
  font-weight: 500;
}

.t-tabs__item--active {
  color: #43a047;
}

.t-tabs__track {
  background-color: #43a047;
}

.t-tab-panel {
  background-color: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .empty-state {
    padding: 80rpx 40rpx;
  }
  
  .empty-collection {
    padding: 80rpx 40rpx;
  }
  
  .collection-list {
    padding: 15rpx;
  }
  
  .collection-item {
    padding: 25rpx;
  }
}
