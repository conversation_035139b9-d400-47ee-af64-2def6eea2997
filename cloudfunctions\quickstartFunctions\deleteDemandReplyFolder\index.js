const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

exports.main = async (event, context) => {
  const { folderPath } = event;

  try {
    const db = cloud.database();
    const demandId = folderPath.replace('demand_replyImages/demandPost_', '');

    const replyResult = await db.collection('demand_reply')
      .where({
        demand_PostId: demandId
      })
      .get();

    let allImageFiles = [];
    replyResult.data.forEach(reply => {
      if (reply.imageList && Array.isArray(reply.imageList)) {
        allImageFiles = allImageFiles.concat(reply.imageList);
      }
    });

    let successCount = 0;
    let failCount = 0;

    if (allImageFiles.length > 0) {
      const deleteResult = await cloud.deleteFile({
        fileList: allImageFiles
      });

      successCount = deleteResult.fileList.filter(file => file.status === 0).length;
      failCount = deleteResult.fileList.length - successCount;
    }
    
    return {
      code: 0,
      msg: `回价文件夹删除完成，成功删除 ${successCount} 个文件`,
      data: {
        replyRecords: replyResult.data.length,
        totalFiles: allImageFiles.length,
        successCount: successCount,
        failCount: failCount
      }
    };

  } catch (error) {
    return {
      code: 1,
      msg: '删除文件夹失败: ' + error.message
    };
  }
};
