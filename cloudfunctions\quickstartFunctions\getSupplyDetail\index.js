// 云函数 - getSupplyDetail   渲染供应细节
// 获取供应信息详情

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 主函数
exports.main = async (event, context) => {
  try {
    // 检查参数
    if (!event.id) {
      return {
        code: 1,
        msg: '缺少必要参数id'
      };
    }

    // 获取帖子详情
    const result = await db.collection('supply_content')
      .doc(event.id)
      .get();

    // 如果找不到帖子
    if (!result || !result.data) {
      return {
        code: 1,
        msg: '找不到对应的供应信息'
      };
    }

    // 返回帖子数据
    return {
      code: 0,
      data: result.data
    };
  } catch (err) {
    console.error('获取供应详情失败:', err);
    return {
      code: 1,
      msg: '获取供应详情失败',
      err: err
    };
  }
}; 