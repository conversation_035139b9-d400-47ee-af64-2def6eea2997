// 云函数入口文件     获取手机号。
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 获取微信上下文
    const wxContext = cloud.getWXContext()
    
    if (!event.code) {
      return {
        success: false,
        errMsg: '缺少code参数'
      }
    }
    
    // 使用code换取手机号
    const resp = await cloud.openapi.phonenumber.getPhoneNumber({
      code: event.code
    })
    
    // 返回手机号信息
    return {
      success: true,
      phoneInfo: resp.phoneInfo || {},
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID,
    }
  } catch (err) {
    console.error('获取手机号失败', err)
    return {
      success: false,
      errMsg: err.message || '获取手机号失败',
      err: err
    }
  }
} 