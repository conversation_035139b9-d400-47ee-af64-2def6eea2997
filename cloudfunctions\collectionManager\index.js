// 云函数 - collectionManager
// 收藏管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      success: false,
      message: '用户身份验证失败'
    }
  }

  const { action, itemId, itemType, itemData, page = 1, pageSize = 10 } = event

  try {
    switch (action) {
      case 'toggle':
        return await toggleCollection(openid, itemId, itemType, itemData)
      case 'list':
        return await getCollectionList(openid, itemType, page, pageSize)
      case 'batchCheck':
        return await batchCheckCollectionStatus(openid, event.itemIds, itemType)
      case 'checkSingle':
        return await checkSingleCollectionStatus(openid, itemId, itemType)
      case 'checkSpecific':
        return await checkSpecificCollectionStatus(openid, event.itemIds, itemType)
      case 'remove':
        return await removeCollection(openid, itemId, itemType)
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    return {
      success: false,
      message: '操作失败: ' + error.message
    }
  }
}

// 切换收藏状态
async function toggleCollection(openid, itemId, itemType, itemData) {
  // 检查是否已收藏
  const existingCollection = await db.collection('user_collections')
    .where({
      _openid: openid,
      itemId: itemId,
      itemType: itemType
    })
    .get()

  if (existingCollection.data.length > 0) {
    // 已收藏，执行取消收藏
    return await removeCollection(openid, itemId, itemType)
  } else {
    // 未收藏，执行添加收藏
    return await addCollection(openid, itemId, itemType, itemData)
  }
}

// 添加收藏
async function addCollection(openid, itemId, itemType, itemData) {
  // 获取用户ID
  const userResult = await db.collection('users').where({ _openid: openid }).get()
  if (userResult.data.length === 0) {
    throw new Error('用户不存在')
  }
  const userId = userResult.data[0]._id

  // 验证项目是否存在
  const collectionName = itemType === 'supply' ? 'supply_content' : 'demand_content'
  const itemResult = await db.collection(collectionName).doc(itemId).get()
  if (!itemResult.data) {
    throw new Error('项目不存在')
  }

  const item = itemResult.data

  // 创建收藏记录
  const collectionData = {
    _openid: openid,
    userId: userId,
    itemId: itemId,
    itemType: itemType,
    createTime: db.serverDate(),
    // 冗余存储关键信息
    itemTitle: item.title || '',
    itemPrice: item.price || 0,
    itemImageUrl: getFirstImageUrl(item),
    itemStatus: item.status || 'active',
    itemCreateTime: item.createTime,
    itemAddress: item.address || '',
    itemContactName: item.contactName || '',
    itemContent: item.content || '' // 添加简介内容
  }

  // 开始事务
  const transaction = await db.startTransaction()

  try {
    // 添加收藏记录
    await transaction.collection('user_collections').add({
      data: collectionData
    })

    // 更新项目的收藏数
    await transaction.collection(collectionName).doc(itemId).update({
      data: {
        collectionCount: _.inc(1)
      }
    })

    // 更新用户的收藏统计
    const updateData = {
      collectionCount: _.inc(1)
    }
    if (itemType === 'supply') {
      updateData.supplyCollectionCount = _.inc(1)
    } else {
      updateData.demandCollectionCount = _.inc(1)
    }

    await transaction.collection('users').doc(userId).update({
      data: updateData
    })

    // 提交事务
    await transaction.commit()

    return {
      success: true,
      message: '收藏成功',
      isCollected: true
    }
  } catch (error) {
    // 回滚事务
    await transaction.rollback()
    throw error
  }
}

// 取消收藏
async function removeCollection(openid, itemId, itemType) {
  // 查找收藏记录
  const collectionResult = await db.collection('user_collections')
    .where({
      _openid: openid,
      itemId: itemId,
      itemType: itemType
    })
    .get()

  if (collectionResult.data.length === 0) {
    return {
      success: true,
      message: '未收藏该项目',
      isCollected: false
    }
  }

  const collection = collectionResult.data[0]
  const userId = collection.userId

  // 开始事务
  const transaction = await db.startTransaction()

  try {
    // 删除收藏记录
    await transaction.collection('user_collections').doc(collection._id).remove()

    // 更新项目的收藏数
    const collectionName = itemType === 'supply' ? 'supply_content' : 'demand_content'
    await transaction.collection(collectionName).doc(itemId).update({
      data: {
        collectionCount: _.inc(-1)
      }
    })

    // 更新用户的收藏统计
    const updateData = {
      collectionCount: _.inc(-1)
    }
    if (itemType === 'supply') {
      updateData.supplyCollectionCount = _.inc(-1)
    } else {
      updateData.demandCollectionCount = _.inc(-1)
    }

    await transaction.collection('users').doc(userId).update({
      data: updateData
    })

    // 提交事务
    await transaction.commit()

    return {
      success: true,
      message: '取消收藏成功',
      isCollected: false
    }
  } catch (error) {
    // 回滚事务
    await transaction.rollback()
    throw error
  }
}

// 获取收藏列表
async function getCollectionList(openid, itemType, page, pageSize) {
  const skip = (page - 1) * pageSize

  let query = {
    _openid: openid
  }

  if (itemType && itemType !== 'all') {
    query.itemType = itemType
  }

  // 获取总数
  const countResult = await db.collection('user_collections').where(query).count()
  const total = countResult.total

  // 获取收藏列表
  const result = await db.collection('user_collections')
    .where(query)
    .orderBy('createTime', 'desc')
    .skip(skip)
    .limit(pageSize)
    .get()

  // 为每个收藏项目获取原始帖子的详细信息，并过滤掉已删除的帖子
  const enrichedData = []
  const invalidCollections = []

  for (const collection of result.data) {
    try {
      const collectionName = collection.itemType === 'supply' ? 'supply_content' : 'demand_content'
      const itemResult = await db.collection(collectionName).doc(collection.itemId).get()

      if (itemResult.data) {
        // 帖子存在，合并收藏信息和原始帖子信息
        enrichedData.push({
          ...collection,
          originalItem: itemResult.data
        })
      } else {
        // 帖子不存在，记录为无效收藏
        console.log('无效收藏:', collection.itemId, '帖子不存在')
        invalidCollections.push(collection)
      }
    } catch (error) {
      console.error('获取原始帖子信息失败:', error)
      // 查询失败的也视为无效收藏
      invalidCollections.push(collection)
    }
  }

  // 异步清理无效收藏（不影响当前请求的响应）
  if (invalidCollections.length > 0) {
    cleanupInvalidCollections(invalidCollections).catch(error => {
      console.error('清理无效收藏失败:', error)
    })
  }

  return {
    success: true,
    data: enrichedData,
    total: enrichedData.length, // 使用过滤后的数据长度
    hasMore: skip + enrichedData.length < total,
    page: page,
    pageSize: pageSize
  }
}

// 清理无效收藏
async function cleanupInvalidCollections(invalidCollections) {
  console.log('开始清理无效收藏，数量:', invalidCollections.length)

  for (const collection of invalidCollections) {
    try {
      // 删除收藏记录
      await db.collection('user_collections').doc(collection._id).remove()

      // 更新用户统计（减少收藏数）
      const updateData = {
        collectionCount: _.inc(-1)
      }
      if (collection.itemType === 'supply') {
        updateData.supplyCollectionCount = _.inc(-1)
      } else {
        updateData.demandCollectionCount = _.inc(-1)
      }

      await db.collection('users').doc(collection.userId).update({
        data: updateData
      })

      console.log('已清理无效收藏:', collection._id)
    } catch (error) {
      console.error('清理单个无效收藏失败:', collection._id, error)
    }
  }

  console.log('无效收藏清理完成')
}

// 获取第一张图片URL的辅助函数
function getFirstImageUrl(item) {
  // 1. 优先检查 newImagesList（新类型图片数组）
  if (item.newImagesList && Array.isArray(item.newImagesList) && item.newImagesList.length > 0) {
    const firstImage = item.newImagesList[0]
    // 如果是对象，取url属性；如果是字符串，直接返回
    return typeof firstImage === 'object' && firstImage.url ? firstImage.url : firstImage
  }

  // 2. 检查 imageList（旧类型图片数组）
  if (item.imageList && Array.isArray(item.imageList) && item.imageList.length > 0) {
    const firstImage = item.imageList[0]
    // 如果是对象，取url属性；如果是字符串，直接返回
    return typeof firstImage === 'object' && firstImage.url ? firstImage.url : firstImage
  }

  // 3. 检查 images（备用字段）
  if (item.images && Array.isArray(item.images) && item.images.length > 0) {
    const firstImage = item.images[0]
    return typeof firstImage === 'object' && firstImage.url ? firstImage.url : firstImage
  }

  // 4. 检查 newImageList（可能的命名变体）
  if (item.newImageList && Array.isArray(item.newImageList) && item.newImageList.length > 0) {
    const firstImage = item.newImageList[0]
    return typeof firstImage === 'object' && firstImage.url ? firstImage.url : firstImage
  }

  // 如果都没有，返回空字符串
  return ''
}

// 检查单个收藏状态
async function checkSingleCollectionStatus(openid, itemId, itemType) {
  const result = await db.collection('user_collections')
    .where({
      _openid: openid,
      itemType: itemType,
      itemId: itemId
    })
    .limit(1)
    .get()

  return {
    success: true,
    isCollected: result.data.length > 0
  }
}

// 检查指定ID列表的收藏状态（精确查询）
async function checkSpecificCollectionStatus(openid, itemIds, itemType) {
  if (!itemIds || itemIds.length === 0) {
    return {
      success: true,
      data: []
    }
  }

  const result = await db.collection('user_collections')
    .where({
      _openid: openid,
      itemType: itemType,
      itemId: _.in(itemIds)
    })
    .field({ itemId: true })
    .get()

  const collectedIds = result.data.map(item => item.itemId)

  return {
    success: true,
    data: collectedIds
  }
}

// 批量检查收藏状态（原有方法，保持兼容性）
async function batchCheckCollectionStatus(openid, itemIds, itemType) {
  if (!itemIds || itemIds.length === 0) {
    return {
      success: true,
      data: []
    }
  }

  const result = await db.collection('user_collections')
    .where({
      _openid: openid,
      itemType: itemType,
      itemId: _.in(itemIds)
    })
    .field({ itemId: true })
    .get()

  const collectedIds = result.data.map(item => item.itemId)

  return {
    success: true,
    data: collectedIds
  }
}
