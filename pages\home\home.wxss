/* pages/home/<USER>/

/* 树叶飘落动画样式 */
page {
  background-color: #f4f9f4; /* 更柔和的浅绿色背景 */
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  height: 100%;
  box-sizing: border-box;
}

/* 搜索栏样式 */
.search-bar-wrapper {
  padding: 0;
  margin: 0; /* 移除上下margin */
  margin-top: -1rpx; /* 轻微上移以消除可能的间隙*/
  width: 100%;
  position: relative;
  overflow: hidden;
}

.search-bar {
  display: flex;
  align-items: center;
  justify-content: center; /* 居中对齐 */
  background: linear-gradient(135deg, #66bb6a, #43a047); /* 渐变绿色背景 */
  width: 100%;
  padding: 20rpx 0; /* 移除左右内边距，只保留上下内边距 */
  box-shadow: 0 4rpx 15rpx rgba(56, 142, 60, 0.15);
  border: 1rpx solid rgba(76, 175, 80, 0.25);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.search-input-container {
  display: flex;
  align-items: center;
  background-color: #f0f0f0; /* 灰色椭圆背景 */
  border-radius: 40rpx; /* 椭圆形 */
  padding: 12rpx 20rpx;
  width: 86%; /* 调整为60%宽度 */
  margin: 0 auto 0 3%; /* 左边距25%，右边自动，向左偏移 */
}

.search-icon-wrapper {
  margin-right: 12rpx;
  display: flex;
  align-items: center;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  display: block;
}

.search-placeholder {
  color: #5f5e5e; /* 灰色文字 */
  font-weight: 600;
  font-size: 28rpx;
  display: inline-block;
  margin-left:1%;
  letter-spacing: 0;
  text-shadow: none;
}



/* 自定义导航栏样式 */
.custom-nav {
  background: linear-gradient(to bottom, #43a047, rgba(165, 214, 167, 0.12)) !important; /* 中绿色到透明淡绿色渐变 */
  box-shadow: none !important;
  margin-bottom: 0 !important;
  z-index: 100 !important;
  border-bottom: none !important;
  padding-bottom: 0 !important;
  height: auto !important;
  position: relative !important;
}

/* 内部元素样式 */
.custom-nav .weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  padding-bottom: 0 !important;
}

.custom-nav .weui-navigation-bar__center {
  color: #2e7d32 !important; /* 改为绿色文字，与应用中其他绿色一致 */
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8); /* 调整文字阴影，增加白色底色提高可读性 */
  letter-spacing: 0.5px;
  font-weight: bold; /* 加粗文字 */
}



/* 树叶动画相关样式 */
.leaves-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

.leaf {
  position: absolute;
  top: -150rpx;
  left: calc(var(--start-pos));
  width: var(--leaf-size);
  height: var(--leaf-size);
  opacity: 0.85;
  animation: leafFall var(--fall-duration) ease-in-out var(--fall-delay) forwards;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
  will-change: transform, top;
  transform-origin: center;
}

.leaf-image {
  width: 100%;
  height: 100%;
  animation: leafSpin 6s linear infinite;
  filter: none; /* 移除绿色色调，恢复原始颜色 */
}

@keyframes leafSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes leafFall {
  0% {
    top: -150rpx;
    transform: translateX(0) rotate(0deg) scale(1);
  }
  20% {
    top: calc(20vh);
    transform: translateX(120rpx) rotate(72deg) scale(0.95);
  }
  40% {
    top: calc(40vh);
    transform: translateX(40rpx) rotate(144deg) scale(0.98);
  }
  60% {
    top: calc(60vh);
    transform: translateX(-60rpx) rotate(216deg) scale(1);
  }
  80% {
    top: calc(80vh);
    transform: translateX(30rpx) rotate(288deg) scale(0.97);
  }
  100% {
    top: 120vh;
    transform: translateX(-20rpx) rotate(360deg) scale(1);
  }
}

/* 确保内容在树叶上方显示 */

/* 页面内容样式 */
.home-content {
  position: fixed;
  top: 90px;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: transparent; /* 去掉背景 */
  margin: 0;
  overflow-y: auto;
  overflow-x: hidden; /* 防止左右滑动 */
  -webkit-overflow-scrolling: touch;
  z-index: 3;
}

/* 交易中心样式 */
.trading-center {
  padding: 0 30rpx 60rpx; /* 减小左右间距，增加卡片显示空间 */
  padding-top: 0;
  position: relative;
  background-color: transparent;
  margin-top: 0;
}

/* 标题区域样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 1;
  padding: 15rpx 0;
  background: linear-gradient(to right, rgba(232, 245, 233, 0.4), rgba(232, 245, 233, 0.8), rgba(232, 245, 233, 0.4));
  border-radius: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(46, 125, 50, 0.08), 0 0 15rpx rgba(76, 175, 80, 0.2);
  overflow: hidden;
}

/* 标题左侧图标样式 */
.header-icon-left {
  width: 80rpx;
  height: 80rpx;
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

/* 标题右侧图标样式 */
.header-icon-right {
  width: 70rpx;
  height: 70rpx;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

/* 标题装饰线左侧 */
.header-decoration:first-child {
  position: relative;
  margin-right: 65rpx; /* 增加与标题的距离 */
}

/* 标题装饰线右侧 */
.header-decoration:last-child {
  position: relative;
  margin-left: 65rpx; /* 增加与标题的距离 */
}

.header-decoration {
  height: 3rpx;
  width: 40rpx;
  background: linear-gradient(to right, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 1)); /* 绿色渐变 */
  border-radius: 3rpx;
}

.header-decoration:last-child {
  background: linear-gradient(to left, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 1)); /* 绿色渐变 */
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2e7d32; /* 森林绿 */
  margin: 0 20rpx;
  position: relative;
}

.header-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 6rpx;
  background-color: #66bb6a; /* 中绿色 */
  border-radius: 3rpx;
}

.center-description {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 1;
}

/* 交易卡片区域样式 */
.trading-cards {
  display: flex;
  flex-direction: column;
  padding: 10rpx 0;
  position: relative;
  z-index: 2;
}

/* 新增卡片横向布局样式 */
.trading-cards-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 20rpx 15rpx 20rpx; /* 恢复原来的内边距 */
  position: relative;
  z-index: 2;
  margin: 0 -30rpx 0 -30rpx; /* 恢复原来的边距 */
  margin-right: -15rpx; /* 单独增加右侧边距，让卡片不贴右边墙 */
  margin-bottom: 0; /* 移除底部margin，避免与搜索栏产生空隙 */
  background: linear-gradient(145deg, #e8f5e9, #c8e6c9);
  border-top: 1px solid rgba(76, 175, 80, 0.3);
  border-bottom: 1px solid rgba(76, 175, 80, 0.3);
  box-shadow: 0 6rpx 16rpx rgba(46, 125, 50, 0.15);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

/* 添加气泡背景效果到trading-cards-row */
.trading-cards-row::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    /* 大气泡 */
    radial-gradient(circle at 80% 20%, rgba(76, 175, 80, 0.2) 0%, rgba(76, 175, 80, 0.2) 5%, transparent 10%),
    radial-gradient(circle at 20% 80%, rgba(76, 175, 80, 0.15) 0%, rgba(76, 175, 80, 0.15) 7%, transparent 14%),
    /* 中气泡 */
    radial-gradient(circle at 40% 30%, rgba(76, 175, 80, 0.12) 0%, rgba(76, 175, 80, 0.12) 4%, transparent 8%),
    radial-gradient(circle at 70% 65%, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.1) 3%, transparent 6%),
    /* 小气泡 */
    radial-gradient(circle at 30% 50%, rgba(76, 175, 80, 0.08) 0%, rgba(76, 175, 80, 0.08) 2%, transparent 4%),
    radial-gradient(circle at 85% 40%, rgba(76, 175, 80, 0.06) 0%, rgba(76, 175, 80, 0.06) 1%, transparent 2%),
    radial-gradient(circle at 60% 85%, rgba(76, 175, 80, 0.07) 0%, rgba(76, 175, 80, 0.07) 1.5%, transparent 3%);
  background-size: 
    200rpx 200rpx, /* 大气泡尺寸 */
    250rpx 250rpx, /* 大气泡尺寸 */
    150rpx 150rpx, /* 中气泡尺寸 */
    120rpx 120rpx, /* 中气泡尺寸 */
    80rpx 80rpx, /* 小气泡尺寸 */
    60rpx 60rpx, /* 小气泡尺寸 */
    70rpx 70rpx; /* 小气泡尺寸 */
  opacity: 0.8;
  z-index: -1;
}

/* 添加背景发光效果 */
.trading-cards-row::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 60%);
  opacity: 0.6;
  z-index: -2;
}

.card-wrapper {
  position: relative;
  margin: 0 10rpx;
  margin-bottom: 0;
  transition: all 0.3s ease;
  transform: perspective(800rpx) rotateX(0);
  display: flex;
  flex: 1;
}

/* 半宽卡片样式 */
.card-half {
  box-sizing: border-box;
  flex: 1; /* 确保两个卡片等宽 */
  max-width: 50%; /* 限制最大宽度 */
}

.card-wrapper:hover {
  transform: translateY(-4rpx) perspective(800rpx) rotateX(2deg);
}

/* 修改卡片顶部样式 */
.card-top {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx; /* 进一步减小下边距 */
  position: relative;
  z-index: 1;
  height: 70rpx; /* 进一步减小高度 */
}

/* 调整图标尺寸 */
.card-icon-wrapper {
  width: 70rpx; /* 进一步减小图标容器 */
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx; /* 减小右边距 */
  position: relative;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(46, 125, 50, 0.2);
  transition: all 0.3s ease;
}

.card-icon {
  width: 40rpx; /* 进一步减小图标 */
  height: 40rpx;
  display: block;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15));
}

.supply-card .card-icon-wrapper {
  background: linear-gradient(135deg, #66bb6a, #2e7d32); /* 中绿到深绿渐变 */
  box-shadow: 0 6rpx 16rpx rgba(102, 187, 106, 0.4), inset 0 2rpx 6rpx rgba(255, 255, 255, 0.6);
  border: 2rpx solid rgba(255, 255, 255, 0.6);
}

.demand-card .card-icon-wrapper {
  background: linear-gradient(135deg, #2e7d32, #1b5e20); /* 深绿到更深绿渐变 */
  box-shadow: 0 6rpx 16rpx rgba(46, 125, 50, 0.4), inset 0 2rpx 6rpx rgba(255, 255, 255, 0.3);
  border: 2rpx solid rgba(255, 255, 255, 0.4);
}

/* 调整标题尺寸 */
.card-title {
  font-size: 32rpx;
  font-weight: 800;
  color: #333;
  margin-bottom: 4rpx;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  letter-spacing: 0rpx;
  white-space: nowrap;
}

/* 卡片标题容器样式 */
.card-title-wrapper {
  display: flex;
  flex-direction: column;
}

/* 卡片口号样式 */
.card-slogan {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: auto;
  width: 100%;
}

/* 调整卡片徽章位置 */
.card-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  font-size: 23rpx;
  padding: 10rpx 30rpx;
  border-radius: 0 24rpx 0 24rpx;
  color: #ffe17e;
  font-weight: 600;
  letter-spacing: 2rpx;
  z-index: 10;
  box-shadow: 0 6rpx 12rpx rgba(46, 125, 50, 0.2);
  transform-origin: top right;
  transition: all 0.3s ease;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  outline: none;
  min-width: 100rpx;
  text-align: center;
}

.supply-badge {
  background: linear-gradient(135deg, #81c784, #4caf50); /* 浅绿到中绿渐变 */
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  padding-right: 35rpx;
}

.demand-badge {
  background: linear-gradient(135deg, #388e3c, #2e7d32); /* 绿到深绿渐变 */
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  padding-right: 35rpx;
}

.card-badge:active {
  transform: scale(0.95) translateY(2rpx);
  opacity: 0.9;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  transition: all 0.1s ease;
}

/* 调整卡片分隔线 */
.card-divider {
  height: 1px; /* 减小高度 */
  background: linear-gradient(to right, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.4), rgba(76, 175, 80, 0.1));
  margin: 8rpx 0 10rpx 0; /* 减小上下边距 */
  position: relative;
  z-index: 1;
  border-radius: 1px;
}

.demand-card .card-divider {
  background: linear-gradient(to right, rgba(46, 125, 50, 0.1), rgba(46, 125, 50, 0.5), rgba(46, 125, 50, 0.1)); /* 更明显的深绿色渐变 */
}

/* 调整描述文字尺寸 */
.card-description {
  font-size: 25rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 12rpx;
  position: relative;
  z-index: 1;
  font-weight: 600;
  text-align: justify;
  padding: 0 6rpx;
  flex: 1;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5rpx;
}

/* 描述文字中第一个字突出效果 */
.card-description .first-char {
  font-size: 41rpx; /* 更大的字体 */
  font-weight: 800; /* 更粗的字体 */
  color: #2e7d32; /* 深绿色 */
  margin-right: 2rpx; /* 右侧留点空间 */
  display: inline-block;
  vertical-align: middle;
  transform: translateY(-2rpx); /* 稍微上移对齐其他文字 */
  text-shadow: 0 2rpx 3rpx rgba(0, 0, 0, 0.15); /* 更强的阴影 */
}

/* 调整口号尺寸 */
.slogan-text {
  top:-15rpx;
  font-size: 25rpx; /* 减小字体 */
  font-style: italic;
  color: #666;
  position: relative;
  display: inline-block;
  font-weight: 500;
  text-align: left;
  margin-top: auto;
  margin-right: auto;
  padding-left: 6rpx;
  margin-bottom: 15rpx;
  max-width: 55%; /* 限制宽度 */
}

.supply-card .slogan-text::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(76, 175, 80, 0.4);
}

.demand-card .slogan-text::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(46, 125, 50, 0.4);
}

.trading-card:active,
.card-hover {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 10rpx rgba(46, 125, 50, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}

/* 确保卡片hover效果不会影响到badge */
.card-hover .card-badge {
  transform: none !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1) !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 按钮hover效果 */
.badge-hover {
  transform: scale(0.95) translateY(2rpx) !important;
  opacity: 0.9 !important;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.08) !important;
  transition: all 0.1s ease !important;
}

/* 返回顶部按钮样式 */
.custom-back-top {
  right: 40rpx;
  bottom: 220rpx; /* 增加底部间距，为客服按钮留出空间 */
}

/* 全宽通知区容器 */
.notice-full-width-container {
  position: relative;
  width: 100%;
  margin-bottom: 20rpx;
  padding: 0;
}

/* 白色通知栏容器样式 */
.notice-container-white {
  position: relative;
  left: -30rpx;
  width: calc(100% + 60rpx);
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.1);
  border-top: 1rpx solid rgba(76, 175, 80, 0.2);
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.2);
  box-sizing: border-box;
  margin: 0;
  margin-top: -1rpx; /* 轻微上移以消除可能的间隙 */
  overflow: hidden;
}

/* 通知项目样式 */
.notice-marquee-wrapper-full {
  height: 355rpx; /* 增加高度，显示更多内容 */
  width: 100%;
  overflow: hidden;
  position: relative;
  -webkit-overflow-scrolling: touch;
  padding: 0;
  box-sizing: border-box;
}

/* 空通知容器样式 */
.notice-empty-container {
  height: 355rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(232, 245, 233, 0.3);
  border-radius: 10rpx;
}

/* 自定义空状态样式 */
.notice-empty-container .t-empty {
  padding: 32rpx;
}

.notice-empty-container .t-empty__description {
  color: #6b7280;
  font-size: 28rpx;
  margin-top: 12rpx;
}

.notice-item {
  padding: 12rpx 16rpx; /* 稍微增加上下padding */
  margin-bottom: 8rpx;
  transition: all 0.05s linear;
  border-radius: 10rpx; /* 增加圆角 */
  position: relative;
  overflow: hidden;
  background: rgba(232, 245, 233, 0.5);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
  line-height: 1.4;
  display: flex;
  flex-direction: column;
  border-left: 4rpx solid #4caf50; /* 加粗左边框 */
  width: 100%;
}

/* 添加悬停效果 */
.notice-item-hover {
  background: rgba(232, 245, 233, 0.8);
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

/* 通知内容样式 */
.notice-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 2rpx 0;
  position: relative; /* 添加相对定位 */
  padding-right: 100rpx; /* 为右侧时间留出空间 */
}

/* 通知用户名样式 */
.notice-username {
  font-size: 26rpx; /* 减小字体大小 */
  font-weight: bold;
  color: #388e3c; /* 绿色 */
  margin-right: 8rpx;
  letter-spacing: 0.5rpx; /* 增加字间距 */
  text-shadow: 0 0.5rpx 0.5rpx rgba(0, 0, 0, 0.05); /* 轻微阴影增强可读性 */
}

/* 通知商品信息样式 */
.notice-product-info {
  color: #d97706; /* 更深的金黄色，类似琥珀色 */
  font-weight: 800; /* 更粗的字体 */
  margin: 0 6rpx;
  font-size: 28rpx; /* 减小字体大小 */
  display: inline-block;
  text-shadow: 0 0.5rpx 0.5rpx rgba(0, 0, 0, 0.1); /* 轻微阴影 */
  letter-spacing: 1rpx;
}

/* 通知内容样式 */
.notice-text {
  color: #e53e3e; /* 更深的红色 */
  font-size: 26rpx; /* 减小字体大小 */
  font-weight: bold; /* 加粗字体 */
  letter-spacing: 0.5rpx; /* 增加字间距 */
}

/* 通知时间样式 */
.notice-time {
  color: #6b7280; /* 更深的灰色 */
  font-size: 22rpx; /* 减小字体大小 */
  font-weight: 500; /* 稍微加粗 */
  position: absolute; /* 使用绝对定位 */
  right: 22rpx; /* 放置在右侧 */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 垂直居中调整 */
}

/* 使用教程卡片样式 */
.tutorial-card {
  display: flex;
  align-items: center;
  background-color: #f9fbe7; /* 浅柠檬绿背景 */
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin: 0 -10rpx 30rpx -10rpx;
  box-shadow: 0 2rpx 8rpx rgba(175, 180, 43, 0.1);
  border-left: 4rpx solid #afb42b; /* 橄榄绿边框 */
  position: relative; /* 添加相对定位 */
  justify-content: space-between; /* 两端对齐 */
}

.tutorial-icon {
  width: 36rpx;
  height: 36rpx;
  background-color: #afb42b; /* 橄榄绿 */
  border-radius: 50%;
  margin-right: 16rpx;
  position: relative;
  flex-shrink: 0;
}

.tutorial-icon::before {
  content: 'i';
  position: absolute;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.tutorial-text {
  font-size: 26rpx;
  color: #827717; /* 深橄榄绿色 */
  line-height: 1.4;
  flex: 1; /* 让文本占据中间空间 */
  margin-right: 20rpx; /* 与客服按钮保持距离 */
}

/* 客服悬浮按钮样式 */
.customer-service-btn {
  position: fixed;
  right: 5%;
  bottom: 300rpx;
  background-color: #43a047; /* 绿色背景 */
  width: 85rpx;
  height: 85rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(67, 160, 71, 0.3);
  z-index: 1000;
  transition: all 0.3s ease;
  padding: 5rpx;
}

/* 悬停效果 */
.customer-service-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(67, 160, 71, 0.2);
}

.customer-service-text {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  margin-top: 5rpx;
}

/* 平台统计区域样式 */
.app-stats-footer {
  margin-top: -45rpx;
  background: linear-gradient(135deg, #b1ec64, #1e7294); /* 绿色渐变 */
  padding: 20rpx; /* 减少上下内边距 */
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(46, 125, 50, 0.25);
  position: relative;
  overflow: hidden;
  color: #ffffff; /* 白色文字 */
  border: none;
}

/* 添加气泡背景效果 */
.app-stats-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    /* 大气泡 - 右上角 */
    radial-gradient(circle at 85% 20%, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.25) 8%, transparent 12%),
    /* 大气泡 - 左下角 */
    radial-gradient(circle at 15% 85%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 10%, transparent 15%),
    /* 中气泡 - 右下角 */
    radial-gradient(circle at 80% 75%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 6%, transparent 9%),
    /* 中气泡 - 左上角 */
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.18) 5%, transparent 8%),
    /* 小气泡 - 散布 */
    radial-gradient(circle at 65% 40%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 2%, transparent 3%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.12) 1.5%, transparent 2.5%),
    radial-gradient(circle at 75% 60%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.1) 1%, transparent 2%),
    radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 1.2%, transparent 2.2%);
  background-size: 
    300rpx 300rpx, /* 大气泡尺寸 */
    350rpx 350rpx, /* 大气泡尺寸 */
    200rpx 200rpx, /* 中气泡尺寸 */
    180rpx 180rpx, /* 中气泡尺寸 */
    100rpx 100rpx, /* 小气泡尺寸 */
    80rpx 80rpx, /* 小气泡尺寸 */
    60rpx 60rpx, /* 小气泡尺寸 */
    70rpx 70rpx; /* 小气泡尺寸 */
  background-position: 
    right top, /* 大气泡位置 */
    left bottom, /* 大气泡位置 */
    right bottom, /* 中气泡位置 */
    left top, /* 中气泡位置 */
    65% 40%, /* 小气泡位置 */
    40% 60%, /* 小气泡位置 */
    75% 60%, /* 小气泡位置 */
    30% 40%; /* 小气泡位置 */
  background-repeat: no-repeat;
  opacity: 0.6;
  z-index: 0;
}

/* 添加顶部装饰线条 */
.app-stats-footer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3rpx;
  background: linear-gradient(to right, 
    rgba(255, 255, 255, 0), 
    rgba(255, 255, 255, 0.7), 
    rgba(255, 255, 255, 0));
  z-index: 1;
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx; /* 减少底部间距 */
  position: relative;
  z-index: 1;
}

.stats-header::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.stats-header-line {
  height: 2rpx;
  width: 60rpx;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.5));
}

.stats-header-line:last-child {
  background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.5));
}

.stats-header-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin: 0 15rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.stats-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 10rpx; /* 减少底部间距 */
}

.stats-row {
  display: flex;
  justify-content: space-between; /* 改回空间两端对齐 */
  margin-bottom: 10rpx;
  align-items: center;
  flex-wrap: nowrap; /* 确保不换行 */
  width: 103%;
  padding: 0 1rpx;
  box-sizing: border-box;
  margin-left: -10rpx;
}

.stats-item {
  width: 100%; /* 增加宽度 */
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 12rpx 8rpx; /* 增加内边距 */
  border-radius: 12rpx;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  margin: 0 2rpx; /* 减少间距以容纳更宽的项目 */
  min-height: 70rpx;
  box-sizing: border-box;
}

/* 为每个统计项设置不同的背景色 */
.stats-item:nth-child(1) {
 /* background: #7ac943; /* 亮绿色 */
 background: #1e7294;
}

.stats-item:nth-child(2) {
 /* background: #9ad85e; /* 浅绿色 */
 background: #3a95b5
}

.stats-item:nth-child(3) {
 /* background: #1e7294; *//* 深蓝色 */
 background: #1e7294;
}

.stats-item:nth-child(4) {
 /* background: #3a95b5; /* 中蓝色 */
}

/* 为每个统计项添加落叶背景 */
.stats-item:nth-child(1)::before,
.stats-item:nth-child(2)::before,
.stats-item:nth-child(3)::before,
.stats-item:nth-child(4)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.15;
  z-index: 0;
  pointer-events: none;
}

/* 第一个统计项 - 右上角落叶 */
.stats-item:nth-child(1)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath fill='%23e6ee9c' d='M80,20 C70,15 55,25 45,35 C35,45 25,60 30,70 C35,80 50,70 60,60 C70,50 85,30 80,20 Z M55,55 C50,60 40,65 35,60 C30,55 35,45 40,40 C45,35 55,30 60,35 C65,40 60,50 55,55 Z'/%3E%3C/svg%3E");
  background-position: 90% 10%;
  background-size: 70% 70%;
  transform: rotate(45deg);
  opacity: 0.12;
}

/* 第二个统计项 - 左下角落叶 */
.stats-item:nth-child(2)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath fill='%23c5e1a5' d='M20,80 C25,70 40,60 50,50 C60,40 70,25 65,15 C60,5 45,15 35,25 C25,35 15,55 20,80 Z M45,45 C50,40 55,30 50,25 C45,20 35,25 30,30 C25,35 20,45 25,50 C30,55 40,50 45,45 Z'/%3E%3C/svg%3E");
  background-position: 10% 90%;
  background-size: 70% 70%;
  transform: rotate(-135deg);
  opacity: 0.12;
}

/* 第三个统计项 - 右下角双叶 */
.stats-item:nth-child(3)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath fill='%23b3e5fc' d='M70,70 C80,60 75,45 65,35 C55,25 40,20 30,30 C20,40 25,55 35,65 C45,75 60,80 70,70 Z M45,55 C35,45 30,35 40,25 C50,15 60,20 70,30 C80,40 75,50 65,60 C55,70 55,65 45,55 Z'/%3E%3C/svg%3E");
  background-position: 90% 90%;
  background-size: 70% 70%;
  transform: rotate(180deg);
  opacity: 0.12;
}

/* 第四个统计项 - 左上角圆叶 */
.stats-item:nth-child(4)::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath fill='%2381d4fa' d='M30,20 C15,25 10,45 20,60 C30,75 50,80 65,70 C80,60 85,40 75,25 C65,10 45,15 30,20 Z M50,50 C40,55 30,50 25,40 C20,30 25,20 35,15 C45,10 55,15 60,25 C65,35 60,45 50,50 Z'/%3E%3C/svg%3E");
  background-position: 10% 10%;
  background-size: 70% 70%;
  transform: rotate(0deg);
  opacity: 0.12;
}

/* 调整光效层，放在叶子上方 */
.stats-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), transparent);
  pointer-events: none;
  z-index: 0;
}

.stats-icon {
  width: 36rpx; /* 进一步减小图标尺寸 */
  height: 36rpx; /* 进一步减小图标尺寸 */
  border-radius: 10rpx;
  margin-right: 6rpx; /* 保持右侧间距 */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  box-shadow: none;
  transform: none;
  transition: all 0.3s ease;
  z-index: 1;
}

/* 调整图标背景色，使其与各自的卡片背景协调 */
.stats-item:nth-child(1) .stats-icon {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
}

.stats-item:nth-child(2) .stats-icon {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
}

.stats-item:nth-child(3) .stats-icon {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
}

.stats-item:nth-child(4) .stats-icon {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
}

.stats-item-hover .stats-icon {
  transform: scale(0.95);
  opacity: 0.9;
}

.stats-data {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  min-width: 0; /* 确保文本可以缩小 */
  flex: 1;
  overflow: hidden; /* 确保内容不溢出 */
}


.stats-number {
  font-size: 25rpx; /* 进一步减小字体大小 */
  font-weight: bold;
  color: #ffd700; /* 淡黄色 */
  margin-bottom: 1rpx; /* 减少底部间距 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.3rpx; /* 减少字母间距 */
  position: relative;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height:1.9; /* 添加行高控制 */
}

/* 为不同项目设置相同的淡黄色 */
.stats-item:nth-child(1) .stats-number {
  color: #fbffef; /* 淡黄色 */
}

.stats-item:nth-child(2) .stats-number {
  color: #f5fdff; /* 淡黄色 */
}

.stats-item:nth-child(3) .stats-number {
  color: #faf4d2; /* 淡黄色 */
}

.stats-item:nth-child(4) .stats-number {
  color: #e6e6e6; /* 淡黄色 */
}

/* 删除星星闪光效果 */
.stats-number::before {
  content: none;
}

.stats-number::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.6), transparent);
  opacity: 0.6;
}

.stats-label {
  font-size: 20rpx; /* 进一步减小字体大小 */
  /*color: #f8e28b;*/ /* 更淡的黄色 */
  color: rgb(250, 239, 189);
  font-weight: 600;
  letter-spacing: 0.3rpx; /* 减少字母间距 */
  text-transform: uppercase;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height: 1.2; /* 添加行高控制 */
}

.footer-copyright {
  text-align: center;
  margin-top: 10rpx;
  padding-top: 10rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between; /* 改为两端对齐 */
  padding-bottom: 10rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
}

.icp-text {
  font-size: 25rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.9);
  font-weight: 700;
  letter-spacing: 0.8rpx;
  margin-right: 0; /* 移除右边距 */
  text-align: left; /* 左对齐文本 */
  margin-left: 130rpx;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.footer-park-image {
  width: 60rpx;
  height: 60rpx;
  opacity: 0.9;
  margin-left: 0;
}

/* 通知头部样式 */
.notice-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 0;
}

.notice-title-icon {
  width: 36rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #66bb6a, #2e7d32);
  border-radius: 50%;
  margin-right: 12rpx;
  position: relative;
}

.notice-title-icon::before,
.notice-title-icon::after {
  content: '';
  position: absolute;
  background-color: white;
  border-radius: 2rpx;
}

.notice-title-icon::before {
  width: 16rpx;
  height: 3rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.notice-title-icon::after {
  width: 3rpx;
  height: 16rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.notice-section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #2e7d32;
}

/* 新增服务卡片容器样式 */
.service-cards-container {
  display: flex;
  flex-direction: column;
  margin: 30rpx 0;
  padding: 20rpx 0; /* 增加上下内边距 */
  position: relative;
  z-index: 2;
  gap: 15rpx; /* 适当增加行间距 */
  min-height: 580rpx; /* 设置最小高度 */
}

/* 服务卡片行布局 */
.service-cards-row {
  display: flex;
  gap: 20rpx;
}

/* 左侧大卡片样式 */
.service-card-large {
  flex: 1;
  height: 360rpx;
}

/* 右侧两卡片布局样式 */
.service-cards-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 右侧小卡片样式 */
.service-card-small {
  height: 170rpx;
}

/* 全宽卡片容器样式 */
.service-card-full-width {
  width: 100%;
  height: 200rpx; /* 降低高度 */
  margin-top: -2rpx; /* 减少上边距，让卡片往上靠 */
  position: relative; /* 为气泡容器提供定位基准 */
  overflow: hidden; /* 隐藏超出边界的气泡 */
}

/* 服务卡片基础样式 */
.service-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 10rpx 30rpx rgba(46, 125, 50, 0.1), 0 2rpx 6rpx rgba(46, 125, 50, 0.08);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform: translateY(0);
  will-change: transform, box-shadow;
  border: 1px solid rgba(76, 175, 80, 0.2);
  height: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

/* 退林还耕卡片样式 */
.forest-return-card {
  background: linear-gradient(135deg, #43a047, #2e7d32); /* 绿色渐变 */
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 添加气泡背景效果 */
.forest-return-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 10% 20%, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.2) 5%, transparent 10%),
                    radial-gradient(circle at 85% 60%, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.15) 5%, transparent 10%),
                    radial-gradient(circle at 40% 80%, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 5%, transparent 10%);
  background-size: 60rpx 60rpx, 40rpx 40rpx, 50rpx 50rpx;
  background-position: 0 0, 20rpx 20rpx, 40rpx 40rpx;
  opacity: 0.4;
  z-index: 0;
}

/* 苗圃转让卡片样式 */
.nursery-card {
  background: linear-gradient(135deg, #66bb6a, #43a047);
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 添加气泡背景效果 */
.nursery-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 15% 25%, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.2) 5%, transparent 10%),
                    radial-gradient(circle at 75% 65%, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.15) 5%, transparent 10%),
                    radial-gradient(circle at 30% 85%, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 5%, transparent 10%);
  background-size: 50rpx 50rpx, 40rpx 40rpx, 60rpx 60rpx;
  background-position: 10rpx 10rpx, 30rpx 30rpx, 50rpx 50rpx;
  opacity: 0.4;
  z-index: 0;
}

/* 绿化设计卡片样式 */
.design-card {
  background: linear-gradient(135deg, #00897b, #00695c); /* 青绿色渐变 */
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 添加气泡背景效果 */
.design-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 20% 30%, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.2) 5%, transparent 10%),
                    radial-gradient(circle at 70% 70%, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.15) 5%, transparent 10%),
                    radial-gradient(circle at 40% 90%, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 5%, transparent 10%);
  background-size: 45rpx 45rpx, 55rpx 55rpx, 35rpx 35rpx;
  background-position: 5rpx 5rpx, 25rpx 25rpx, 45rpx 45rpx;
  opacity: 0.4;
  z-index: 0;
}

/* 视频教程卡片样式 */
.video-tutorial-card {
  background: linear-gradient(8deg, #b1ec64 1%, #1e7294 60%); /* 绿色渐变 */
  border: none;
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 添加气泡背景效果 - 使用app-stats-footer的气泡样式 */
.video-tutorial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    /* 大气泡 - 右上角 */
    radial-gradient(circle at 85% 20%, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.25) 8%, transparent 12%),
    /* 大气泡 - 左下角 */
    radial-gradient(circle at 15% 85%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 10%, transparent 15%),
    /* 中气泡 - 右下角 */
    radial-gradient(circle at 80% 75%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 6%, transparent 9%),
    /* 中气泡 - 左上角 */
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.18) 5%, transparent 8%),
    /* 小气泡 - 散布 */
    radial-gradient(circle at 65% 40%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 2%, transparent 3%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.12) 1.5%, transparent 2.5%),
    radial-gradient(circle at 75% 60%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.1) 1%, transparent 2%),
    radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 1.2%, transparent 2.2%);
  background-size:
    300rpx 300rpx, /* 大气泡尺寸 */
    350rpx 350rpx, /* 大气泡尺寸 */
    200rpx 200rpx, /* 中气泡尺寸 */
    180rpx 180rpx, /* 中气泡尺寸 */
    100rpx 100rpx, /* 小气泡尺寸 */
    80rpx 80rpx, /* 小气泡尺寸 */
    60rpx 60rpx, /* 小气泡尺寸 */
    70rpx 70rpx; /* 小气泡尺寸 */
  background-position:
    right top, /* 大气泡位置 */
    left bottom, /* 大气泡位置 */
    right bottom, /* 中气泡位置 */
    left top, /* 中气泡位置 */
    65% 40%, /* 小气泡位置 */
    40% 60%, /* 小气泡位置 */
    75% 60%, /* 小气泡位置 */
    30% 40%; /* 小气泡位置 */
  background-repeat: no-repeat;
  opacity: 0.6;
  z-index: 0;
}

/* 卡片内容样式 */
.service-card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  flex: 1;
  position: relative;
  z-index: 1;
}

/* 卡片标题样式 */
.service-card-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-block;
}

/* 服务卡片中的突出字符 */
.service-card-title .highlight-char {
  font-size: 1.5em;
  font-weight: 900;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.25);
  position: relative;
  display: inline-block;
  margin-right: -3rpx;
  transform: translateY(2rpx);
}

/* 为不同卡片定制突出字符颜色 */
.forest-return-card .service-card-title .highlight-char {
  color: #ffeb3b; /* 明亮的黄色 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  font-weight: 900;
}

.nursery-card .service-card-title .highlight-char {
  color: #fff59d; /* 浅黄色 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  font-weight: 900;
}

.design-card .service-card-title .highlight-char {
  color: #e6ee9c; /* 淡黄绿色 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  font-weight: 900;
}

.video-tutorial-card .service-card-title .highlight-char {
  color: #fff59d; /* 淡绿色，与主题一致 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  font-weight: 900;
}

/* 特定卡片颜色定制 */
/* 退林还耕卡片文字颜色 */
.forest-return-card .service-card-title {
  color: #c5e1a5; /* 淡黄绿色 */
  font-weight: 800;
}

/* 苗圃转让卡片文字颜色 */
.nursery-card .service-card-title {
  color: #dcedc8; /* 极浅绿色 */
  font-weight: 800;
}

/* 绿化设计卡片文字颜色 */
.design-card .service-card-title {
  color: #b2dfdb; /* 淡青绿色 */
  font-weight: 800;
}

/* 视频教程卡片文字颜色 */
.video-tutorial-card .service-card-title {
 
  color: #e8f5e8; /* 极淡绿色，与主题一致 */
  font-weight: 800;
}

/* 卡片描述样式 */
.service-card-desc {
  font-size: 26rpx; /* 增大字体 */
  opacity: 1; /* 完全不透明 */
  margin-bottom: 20rpx;
  line-height: 1.5;
  color: rgba(255, 255, 255, 1); /* 纯白色 */
  font-weight: 600; /* 加粗 */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2); /* 增加阴影 */
}

/* 卡片口号样式 */
.service-card-slogan {
  font-size: 24rpx; /* 增大字体 */
  opacity: 0.95; /* 几乎不透明 */
  font-style: italic;
  margin-top: auto;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500; /* 加粗 */
  text-shadow: 0 1rpx 1rpx rgba(0, 0, 0, 0.15); /* 轻微阴影 */
}

/* 卡片图标包装器样式 */
.service-card-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: transparent; /* 去掉白色背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  align-self: center;
  position: relative;
  z-index: 1;
}

/* 小卡片的图标包装器样式  75  , 75rpx 原来*/
.service-card-icon-wrapper.small {
  width: 85rpx; 
  height: 85rpx;
  background-color: transparent; /* 去掉白色背景 */
}

/* 卡片图标样式   原来是80 */
.service-card-icon {
  width: 100%; /* 放大图标 */
  height: 100%; /* 放大图标 */
  opacity: 0.9;
}

/* 卡片悬停效果 */
.service-card:active,
.service-card.card-hover {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 10rpx rgba(30, 58, 138, 0.1);
}

/* 服务卡片中突出字符的样式 */
.trading-card .highlight-char {
  font-size: 1.3em;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);
  transform: translateY(2rpx);
  display: inline-block;
  margin-right: -3rpx;
}

.supply-card .highlight-char {
  color: #66bb6a; /* 浅绿色，与supply-card的背景渐变起始色匹配 */
}

.demand-card .highlight-char {
  color: #2e7d32; /* 深绿色，与demand-card的背景渐变终止色匹配 */
}

/* 突出文本的样式 */
.highlight-text {
  font-weight: 800; /* 更粗 */
  color: #ffffff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.25); /* 增强阴影 */
  position: relative;
  display: inline-block;
  letter-spacing: 0.5rpx; /* 增加字间距 */
}

/* 服务卡片内突出文本的特定样式 */
.service-card .highlight-text {
  position: relative;
}

.service-card .highlight-text::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 100%;
  height: 2rpx;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 1rpx;
}

/* 为不同卡片定制突出文本颜色 */
.forest-return-card .highlight-text {
  color: #ffffff; /* 纯白色 */
}

.nursery-card .highlight-text {
  color: #ffffff; /* 纯白色 */
}

.design-card .highlight-text {
  color: #ffffff; /* 纯白色 */
}

/* 退林还耕卡片中的下划线 */
.forest-return-card .highlight-text::after {
  background-color: rgba(197, 225, 165, 0.7); /* 淡绿色 */
}

/* 苗圃转让卡片中的下划线 */
.nursery-card .highlight-text::after {
  background-color: rgba(220, 237, 200, 0.7); /* 极浅绿色 */
}

/* 绿化设计卡片中的下划线 */
.design-card .highlight-text::after {
  background-color: rgba(178, 223, 219, 0.7); /* 淡青绿色 */
}

/* 卡片口号图片样式 */
.card-slogan-image {
  
  width: 45%; /* 减小宽度，给左侧腾出空间 */
  height: auto;
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 3rpx 8rpx 6rpx 0;
  min-height: 35rpx;
}

.card-slogan-image image {
  width: 100%; /* 图片占满容器宽度 */
  height: auto;
  display: block;
}

/* 为卡片添加装饰元素 */
.trading-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 16rpx 16rpx;
  box-shadow: 0 10rpx 30rpx rgba(46, 125, 50, 0.1), 0 2rpx 6rpx rgba(46, 125, 50, 0.08);
  position: relative;
  overflow: hidden;
  margin: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform: translateY(0);
  will-change: transform, box-shadow;
  border: 1px solid rgba(76, 175, 80, 0.2);
  height: 343rpx; /* 设置固定高度为343rpx */
  min-height: auto; /* 移除最小高度限制 */
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 8rpx;
  box-sizing: border-box; /* 确保padding不会增加实际高度 */
}

.supply-card {
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 1), rgba(232, 245, 233, 1)); /* 白色到淡绿色渐变 */
}

.demand-card {
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 1), rgba(220, 237, 222, 1)); /* 白色到更深的淡绿色渐变 */
}

/* 供应卡片装饰元素 */
.supply-card::before {
  content: '';
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, transparent 50%, rgba(102, 187, 106, 0.15) 50%);
  border-radius: 0 0 0 100%;
  z-index: 0;
}

.supply-card::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: -10rpx;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(315deg, transparent 50%, rgba(102, 187, 106, 0.1) 50%);
  border-radius: 0 100% 0 0;
  z-index: 0;
}

/* 求购卡片装饰元素 */
.demand-card::before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(225deg, transparent 50%, rgba(46, 125, 50, 0.15) 50%);
  border-radius: 0 0 100% 0;
  z-index: 0;
}

.demand-card::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(45deg, transparent 50%, rgba(46, 125, 50, 0.1) 50%);
  border-radius: 100% 0 0 0;
  z-index: 0;
}

/* 为卡片添加圆点装饰 */
.card-dots-decoration {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 0;
  opacity: 0.3;
}

.supply-card .card-dots-decoration {
  background-image: 
    radial-gradient(circle at 20% 30%, #66bb6a 0, #66bb6a 2rpx, transparent 3rpx),
    radial-gradient(circle at 80% 40%, #66bb6a 0, #66bb6a 2rpx, transparent 3rpx),
    radial-gradient(circle at 40% 80%, #66bb6a 0, #66bb6a 2rpx, transparent 3rpx),
    radial-gradient(circle at 70% 90%, #66bb6a 0, #66bb6a 2rpx, transparent 3rpx);
  background-size: 180rpx 180rpx;
  background-position: 0 0, 30rpx 30rpx, 60rpx 60rpx, 90rpx 90rpx;
}

.demand-card .card-dots-decoration {
  background-image: 
    radial-gradient(circle at 25% 25%, #2e7d32 0, #2e7d32 2rpx, transparent 3rpx),
    radial-gradient(circle at 75% 35%, #2e7d32 0, #2e7d32 2rpx, transparent 3rpx),
    radial-gradient(circle at 35% 75%, #2e7d32 0, #2e7d32 2rpx, transparent 3rpx),
    radial-gradient(circle at 65% 85%, #2e7d32 0, #2e7d32 2rpx, transparent 3rpx);
  background-size: 170rpx 170rpx;
  background-position: 0 0, 35rpx 35rpx, 70rpx 70rpx, 105rpx 105rpx;
}

/* 为卡片添加装饰线条 */
.card-line-decoration {
  position: absolute;
  width: 25rpx; /* 进一步减小尺寸 */
  height: 25rpx;
  border: 1px solid rgba(76, 175, 80, 0.2); /* 减小边框 */
  z-index: 0;
}

.supply-card .card-line-decoration.top-right {
  top: 12rpx;
  right: 12rpx;
  border-left: none;
  border-bottom: none;
  border-color: rgba(102, 187, 106, 0.3);
}

.supply-card .card-line-decoration.bottom-left {
  bottom: 12rpx;
  left: 12rpx;
  border-right: none;
  border-top: none;
  border-color: rgba(102, 187, 106, 0.3);
}

.demand-card .card-line-decoration.top-left {
  top: 12rpx;
  left: 12rpx;
  border-right: none;
  border-bottom: none;
  border-color: rgba(46, 125, 50, 0.3);
}

.demand-card .card-line-decoration.bottom-right {
  bottom: 12rpx;
  right: 12rpx;
  border-left: none;
  border-top: none;
  border-color: rgba(46, 125, 50, 0.3);
}

/* 卡片悬停效果 */
.trading-card:active,
.card-hover {
  
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 10rpx rgba(46, 125, 50, 0.1);
}

/* 卡片激活时图标效果 */
.card-hover .card-icon-wrapper {
  transform: scale(1.05);
  box-shadow: 0 10rpx 25rpx rgba(46, 125, 50, 0.25);
}

/* 底部安全区域，防止内容被tabbar遮挡 */
.bottom-safe-area {
  height: 140rpx; /* 增加安全距离 */
  width: 100%;
  margin-top: 30rpx;
}

.posts-icon {
  background: linear-gradient(135deg, #81c784, #43a047);
}

.posts-icon::before,
.posts-icon::after {
  content: '';
  position: absolute;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.posts-icon::before {
  width: 22rpx;
  height: 22rpx;
  border: 2rpx solid white;
  border-radius: 3rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.posts-icon::after {
  width: 14rpx;
  height: 2rpx;
  top: 60%;
  left: 50%;
  transform: translateX(-50%);
}

.logins-icon {
  background: linear-gradient(135deg, #66bb6a, #388e3c);
}

.logins-icon::before {
  content: '';
  position: absolute;
  width: 22rpx;
  height: 22rpx;
  border: 2rpx solid white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.logins-icon::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 10rpx;
  background-color: white;
  border-radius: 4rpx;
  bottom: 40%;
  left: 60%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.users-icon {
  background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.users-icon::before {
  content: '';
  position: absolute;
  width: 18rpx;
  height: 18rpx;
  border: 2rpx solid white;
  border-radius: 50%;
  top: 35%;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.users-icon::after {
  content: '';
  position: absolute;
  width: 26rpx;
  height: 14rpx;
  border: 2rpx solid white;
  border-radius: 12rpx 12rpx 0 0;
  border-bottom: none;
  bottom: 30%;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.active-icon {
  background: linear-gradient(135deg, #aed581, #7cb342);
}

.active-icon::before {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background-color: white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: -10rpx 0 0 white, 10rpx 0 0 white, -10rpx 0 2px rgba(0, 0, 0, 0.1), 10rpx 0 2px rgba(0, 0, 0, 0.1);
}

.stats-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.1);
  opacity: 0.95;
}

/* 气泡动画样式 */
.bubbles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% + 60rpx); /* 扩展高度，让气泡可以从容器外生成 */
  pointer-events: none; /* 不阻挡用户点击卡片 */
  z-index: 1; /* 确保在卡片内容下方 */
}

.bubble {
  position: absolute;
  bottom: -60rpx; /* 从容器外部（下方）开始生成 */
  width: var(--bubble-size);
  height: var(--bubble-size);
  /* 参考app-stats-footer的模糊气泡样式 */
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, var(--bubble-opacity)), rgba(255, 255, 255, 0.1));
  border-radius: 50%;
  left: var(--start-pos);
  opacity: var(--bubble-opacity);
  animation: bubbleRise var(--bubble-duration) linear var(--bubble-delay) forwards;
  /* 模糊效果，类似app-stats-footer的气泡 */
  filter: blur(1rpx);
  box-shadow:
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.4),
    inset 3rpx 3rpx 6rpx rgba(255, 255, 255, 0.1),
    0 0 8rpx rgba(255, 255, 255, 0.2);
}

/* 气泡上升动画 - 从容器外部上升到容器顶部，无淡出效果 */
@keyframes bubbleRise {
  0% {
    transform: translateY(0) translateX(0) scale(0.8);
    opacity: 0;
  }

  10% {
    transform: translateY(-20rpx) translateX(-5rpx) scale(1);
    opacity: var(--bubble-opacity);
  }

  25% {
    transform: translateY(-80rpx) translateX(-12rpx) scale(1.1);
    opacity: var(--bubble-opacity);
  }

  50% {
    transform: translateY(-140rpx) translateX(8rpx) scale(0.95);
    opacity: var(--bubble-opacity);
  }

  75% {
    transform: translateY(-200rpx) translateX(-10rpx) scale(1.05);
    opacity: var(--bubble-opacity);
  }

  90% {
    transform: translateY(-250rpx) translateX(5rpx) scale(0.9);
    opacity: var(--bubble-opacity);
  }

  100% {
    transform: translateY(-280rpx) translateX(3rpx) scale(0.7);
    opacity: var(--bubble-opacity);
  }
}
