<view wx:if="{{active !== -1 && !hidden}}">
  <view class="custom-tab-bar-wrapper">
    <t-tab-bar value="{{active}}" bindchange="onChange" class="custom-tab-bar" theme="tag" split="{{false}}">
      <!-- 前两个选项卡 -->
      <t-tab-bar-item
        wx:for="{{list}}"
        wx:key="index"
        wx:if="{{index < 2}}"
        value="{{index}}"
        icon="{{active === index ? item.selectedIcon : item.icon}}"
        class="tab-item-{{index}} {{active === index ? 'active-tab-item' : ''}}"
      >
        <text style="color: {{active === index ? item.activeColor : item.color}}; font-weight: {{active === index ? 'bold' : 'normal'}};">{{item.text}}</text>
      </t-tab-bar-item>
      
      <!-- 后两个选项卡 -->
      <t-tab-bar-item
        wx:for="{{list}}"
        wx:key="index"
        wx:if="{{index >= 2}}"
        value="{{index}}"
        icon="{{active === index ? item.selectedIcon : item.icon}}"
        class="tab-item-{{index}} {{active === index ? 'active-tab-item' : ''}}"
      >
        <text style="color: {{active === index ? item.activeColor : item.color}}; font-weight: {{active === index ? 'bold' : 'normal'}};">{{item.text}}</text>
      </t-tab-bar-item>
    </t-tab-bar>
    
    <!-- 中间的发布按钮，在所有tabbar页面都显示  这里原来是只有home页面才显示--> 
    <view class="center-publish-button" bindtap="onPublishTap">
      <view class="publish-button-inner">
        <image src="/assets/tabbar/up.png" class="publish-icon"></image>
      </view>
      <text class="publish-text">发布</text>
    </view>
  </view>
</view> 