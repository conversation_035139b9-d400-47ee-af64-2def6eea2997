// 云函数入口文件
const getSupplyListWithDistance = require('./getSupplyListWithDistance/index');
const getSupplyDetail = require('./getSupplyDetail/index');
const getMySupplyList = require('./getMySupplyList/index');
const getDemandList = require('./getDemandList/index');
const getDemandDetail = require('./getDemandDetail/index');
const getMyDemandList = require('./getMyDemandList/index');
const getMyQuoteList = require('./getMyQuoteList/index');
const getAllQuoteList = require('./getAllQuoteList/index'); // 新增：获取所有报价列表（管理员）
const updateSupply = require('./updateSupply/index');
const updateDemand = require('./updateDemand/index');
const createPostNotice = require('./createPostNotice/index');
const login = require('./login/index');
const createUser = require('./createUser/index');
const getPhoneNumber = require('./getPhoneNumber/index');
const loadFeaturedGoods = require('./loadFeaturedGoods/index');
const loadHotGoods = require('./loadHotGoods/index');
const getNurseryList = require('./getNurseryList/index');
const getNurseryDetail = require('./getNurseryDetail/index');
const deleteDemandReplyFolder = require('./deleteDemandReplyFolder/index');
const getDemandReplies = require('./getDemandReplies/index');
const getTotalNewRepliesCount = require('./getTotalNewRepliesCount/index');

// 云函数入口函数
exports.main = async (event, context) => {
  switch (event.type) {
    case 'getSupplyListWithDistance':
      return await getSupplyListWithDistance.main(event, context);
    case 'getSupplyDetail':
      return await getSupplyDetail.main(event, context);
    case 'getMySupplyList':
      return await getMySupplyList.main(event, context);
    case 'getDemandList':
      return await getDemandList.main(event, context);
    case 'getDemandDetail':
      return await getDemandDetail.main(event, context);
    case 'getMyDemandList':
      return await getMyDemandList.main(event, context);
    case 'getTotalNewRepliesCount':
      return await getTotalNewRepliesCount.main(event, context);
    case 'getMyQuoteList':
      return await getMyQuoteList.main(event, context);
    case 'getAllQuoteList':
      return await getAllQuoteList.main(event, context);
    case 'updateSupply':
      return await updateSupply.main(event, context);
    case 'updateDemand':
      return await updateDemand.main(event, context);
    case 'createPostNotice':
      return await createPostNotice.main(event, context);
    case 'login':
      return await login.main(event, context);
    case 'createUser':
      return await createUser.main(event, context);
    case 'getPhoneNumber':
      return await getPhoneNumber.main(event, context);
    case 'loadFeaturedGoods':
      return await loadFeaturedGoods.main(event, context);
    case 'loadHotGoods':
      return await loadHotGoods.main(event, context);
    case 'getNurseryList':
      return await getNurseryList.main(event, context);
    case 'getNurseryDetail':
      return await getNurseryDetail.main(event, context);
    case 'deleteDemandReplyFolder':
      return await deleteDemandReplyFolder.main(event, context);
    case 'getDemandReplies':
      return await getDemandReplies.main(event, context);
    default:
      return {
        code: 1,
        msg: '未知的函数类型'
      };
  }
};