// 云函数 getDemandList  后台渲染demand页面
const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();
const _ = db.command;
const $ = db.command.aggregate;
const MAX_LIMIT = 100; // 云函数单次查询最大数量

// 格式化时间为多久之前
function formatTimeAgo(dateObj) {
  if (!dateObj) return '';
  
  // 如果是字符串，转换为Date对象
  const date = typeof dateObj === 'string' ? new Date(dateObj) : dateObj;
  const now = new Date();
  
  const diff = Math.floor((now - date) / 1000); // 差异，单位：秒
  
  if (diff < 60) {
    return '刚刚';
  } else if (diff < 3600) {
    return Math.floor(diff / 60) + '分钟前';
  } else if (diff < 86400) {
    return Math.floor(diff / 3600) + '小时前';
  } else if (diff < 2592000) {
    return Math.floor(diff / 86400) + '天前';
  } else if (diff < 31536000) {
    return Math.floor(diff / 2592000) + '个月前';
  } else {
    return Math.floor(diff / 31536000) + '年前';
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
   const params = event.params || event;
  const {
    page = 1,
    pageSize = 10,
    tabActive = 0,
    tabs = ['全部'],
    searchValue = '',
    activeFilters = {}
  } = params;

  try {
    // 构建查询条件
    let query = buildQuery(tabActive, tabs, searchValue, activeFilters);
    
    // 计算跳过的数量
    const skip = (page - 1) * pageSize;
    
    // 获取总数
    const countResult = await db.collection('demand_content').where(query).count();
    const total = countResult.total;
    
    // 设置默认排序方式为创建时间降序
    let orderField = 'createTime';
    let orderDirection = 'desc';
    
    // 构建数据库查询
    let dbQuery = db.collection('demand_content').where(query);
    
    // 添加排序
    dbQuery = dbQuery.orderBy(orderField, orderDirection);
    
    // 分页
    dbQuery = dbQuery.skip(skip).limit(pageSize);
    
    // 执行查询
    const queryResult = await dbQuery.get();
    const rawData = queryResult.data;
    
    // 处理数据，格式化时间等
    const processedData = processData(rawData);
    
    return {
      code: 0,
      data: processedData,
      total: total,
      hasMore: skip + processedData.length < total
    };
  } catch (err) {
    return {
      code: -1,
      msg: '获取数据失败',
      error: err
    };
  }
};

/**
 * 构建查询条件
 */
function buildQuery(tabActive, tabs, searchValue, activeFilters) {
  let query = {};
  
  // 根据分类标签筛选
  if (tabActive > 0 && tabs.length > tabActive) {
    const category = tabs[tabActive];
    query.category = category;
  }
  
  // 如果有搜索关键词
  if (searchValue) {
    // 使用正则表达式进行模糊搜索
    const keyword = searchValue;
    const keywordQuery = _.or([
      {
        title: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      },
      {
        content: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      }
    ]);
    
    // 合并分类查询和关键词查询
    if (tabActive > 0 && tabs.length > tabActive) {
      // 已有分类查询，需要同时满足分类和关键词条件
      query = _.and([
        { category: tabs[tabActive] },
        keywordQuery
      ]);
    } else {
      // 没有分类查询，只有关键词查询
      query = keywordQuery;
    }
  }
  
  // 添加筛选条件
  let filterConditions = [];
  
  // 数量筛选
  if (activeFilters.isQuantityExact && activeFilters.exactQuantity !== '') {
    // 精确数量筛选
    filterConditions.push({ quantity: Number(activeFilters.exactQuantity) });
  } else {
    // 区间数量筛选
    if (activeFilters.minQuantity !== undefined && activeFilters.minQuantity !== '') {
      filterConditions.push({ quantity: _.gte(Number(activeFilters.minQuantity)) });
    }
    if (activeFilters.maxQuantity !== undefined && activeFilters.maxQuantity !== '') {
      filterConditions.push({ quantity: _.lte(Number(activeFilters.maxQuantity)) });
    }
  }
  
  // 高度筛选
  if (activeFilters.isHeightExact && activeFilters.exactHeight !== '') {
    // 精确高度筛选
    filterConditions.push({ height: Number(activeFilters.exactHeight) });
  } else {
    // 区间高度筛选
    if (activeFilters.minHeight !== undefined && activeFilters.minHeight !== '') {
      filterConditions.push({ height: _.gte(Number(activeFilters.minHeight)) });
    }
    if (activeFilters.maxHeight !== undefined && activeFilters.maxHeight !== '') {
      filterConditions.push({ height: _.lte(Number(activeFilters.maxHeight)) });
    }
  }
  
  // 冠幅筛选
  if (activeFilters.isCanopyExact && activeFilters.exactCanopy !== '') {
    // 精确冠幅筛选
    filterConditions.push({ canopy: Number(activeFilters.exactCanopy) });
  } else {
    // 区间冠幅筛选
    if (activeFilters.minCanopy !== undefined && activeFilters.minCanopy !== '') {
      filterConditions.push({ canopy: _.gte(Number(activeFilters.minCanopy)) });
    }
    if (activeFilters.maxCanopy !== undefined && activeFilters.maxCanopy !== '') {
      filterConditions.push({ canopy: _.lte(Number(activeFilters.maxCanopy)) });
    }
  }
  
  // 米径筛选
  if (activeFilters.isMeterDiameterExact && activeFilters.exactMeterDiameter !== '') {
    // 精确米径筛选
    filterConditions.push({ meter_diameter: Number(activeFilters.exactMeterDiameter) });
  } else {
    // 区间米径筛选
    if (activeFilters.minMeterDiameter !== undefined && activeFilters.minMeterDiameter !== '') {
      filterConditions.push({ meter_diameter: _.gte(Number(activeFilters.minMeterDiameter)) });
    }
    if (activeFilters.maxMeterDiameter !== undefined && activeFilters.maxMeterDiameter !== '') {
      filterConditions.push({ meter_diameter: _.lte(Number(activeFilters.maxMeterDiameter)) });
    }
  }
  
  // 杯口筛选
  if (activeFilters.isCupExact && activeFilters.exactCup !== '') {
    // 精确杯口筛选
    filterConditions.push({ cup: Number(activeFilters.exactCup) });
  } else {
    // 区间杯口筛选
    if (activeFilters.minCup !== undefined && activeFilters.minCup !== '') {
      filterConditions.push({ cup: _.gte(Number(activeFilters.minCup)) });
    }
    if (activeFilters.maxCup !== undefined && activeFilters.maxCup !== '') {
      filterConditions.push({ cup: _.lte(Number(activeFilters.maxCup)) });
    }
  }
  
  // 质量筛选
  if (activeFilters.quality && activeFilters.quality !== 'all') {
    filterConditions.push({ quality: activeFilters.quality });
  }
  
  // 省份筛选
  if (activeFilters.province && activeFilters.province !== 'all') {
    filterConditions.push({
      buyArea: db.RegExp({
        regexp: activeFilters.province,
        options: 'i'
      })
    });
  }
  
  // 采购地筛选
  if (activeFilters.purchaseArea) {
    // 如果是全国，不添加筛选条件
    if (activeFilters.purchaseArea !== '000') {
      // 如果是省级编码（如果以_empty结尾，去掉_empty部分）
      const areaCode = activeFilters.purchaseArea.endsWith('_empty') 
        ? activeFilters.purchaseArea.split('_')[0] 
        : activeFilters.purchaseArea;
      
      // 如果是省级编码（6位数字且后4位为0000）
      if (areaCode.length === 6 && areaCode.endsWith('0000')) {
        // 匹配省份前缀（前2位）
        const provincePrefix = areaCode.substring(0, 2);
        filterConditions.push({
          buyArea: db.RegExp({
            regexp: provincePrefix,
            options: 'i'
          })
        });
      } else {
        // 精确匹配城市编码或包含文本
        filterConditions.push({
          buyArea: db.RegExp({
            regexp: areaCode,
            options: 'i'
          })
        });
      }
    }
  }
  
  // 合并所有筛选条件
  if (filterConditions.length > 0) {
    if (Object.keys(query).length > 0) {
      // 已有查询条件，需要与筛选条件同时满足
      query = _.and([query, ...filterConditions]);
    } else {
      // 没有查询条件，只有筛选条件
      query = filterConditions.length === 1 ? filterConditions[0] : _.and(filterConditions);
    }
  }
  
  // 如果没有任何查询条件，添加时间限制避免全表扫描
  if (Object.keys(query).length === 0) {
    // 添加最近180天的时间限制
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setDate(sixMonthsAgo.getDate() - 180);
    query.createTime = _.gte(sixMonthsAgo);
  }
  
  return query;
}

/**
 * 处理原始数据，格式化时间等
 */
function processData(rawData) {
  return rawData.map(item => {
    // 处理标题，确保有【求购】前缀
    const originalTitle = item.title || '';
    const titlePrefix = '【求购】';
    const titleContent = originalTitle.startsWith(titlePrefix) ? originalTitle.substring(titlePrefix.length) : originalTitle;
    
    return {
      id: item._id,
      title: titleContent,
      content: item.content || '',
      quantity: item.quantity || '',
      unit: item.unit || '棵',
      quality: item.quality || '',
      height: item.height || null,
      canopy: item.canopy || null,
      meter_diameter: item.meter_diameter || null,
      ground_diameter: item.ground_diameter || null,
      cup: item.cup || null,
      branchPos: item.branchPos || null,
      contactName: item.contactName || '',
      phoneNumber: item.phoneNumber || '',
      imageList: item.imageList || [],
      createTime: item.createTime,
      timeAgo: formatTimeAgo(item.createTime),
      buyArea: item.buyArea || '',
      usingAddress: item.usingAddress || [],
      outDate: item.outDate || '1天', // 添加求购期限字段，默认为1天
      rawData: item // 保存原始数据以便后续使用
    };
  });
} 