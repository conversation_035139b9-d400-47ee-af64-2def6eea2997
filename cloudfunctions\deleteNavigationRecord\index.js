// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { recordId } = event

    // 参数验证
    if (!recordId) {
      return {
        success: false,
        message: '缺少记录ID'
      }
    }

    // 先查询记录是否存在且属于当前用户
    const record = await db.collection('navigation_records').doc(recordId).get()
    
    if (!record.data) {
      return {
        success: false,
        message: '记录不存在'
      }
    }

    // 验证记录是否属于当前用户
    if (record.data.userId !== wxContext.OPENID) {
      return {
        success: false,
        message: '无权限删除此记录'
      }
    }

    // 删除记录
    await db.collection('navigation_records').doc(recordId).remove()

    return {
      success: true,
      message: '删除成功'
    }

  } catch (error) {
    console.error('删除导航记录失败:', error)
    return {
      success: false,
      message: '删除失败',
      error: error.message
    }
  }
}
