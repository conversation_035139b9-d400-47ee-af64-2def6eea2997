// 云函数 - updateSupply
// 更新供应信息

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 主函数
exports.main = async (event, context) => {
  try {
    // 检查参数
    if (!event.id) {
      return {
        code: 1,
        msg: '缺少必要参数id'
      };
    }

    if (!event.updateData || Object.keys(event.updateData).length === 0) {
      return {
        code: 1,
        msg: '缺少更新数据'
      };
    }

    // 获取用户信息
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;

    if (!openid) {
      return {
        code: 1,
        msg: '获取用户身份失败'
      };
    }

    console.log('更新请求数据:', { 
      postId: event.id, 
      openid: openid,
      updateDataKeys: Object.keys(event.updateData)
    });

    // 先检查帖子是否存在且属于当前用户
    const post = await db.collection('supply_content')
      .doc(event.id)
      .get();

    if (!post || !post.data) {
      return {
        code: 1,
        msg: '找不到对应的供应信息'
      };
    }

    // 检查帖子是否属于当前用户
    if (post.data._openid !== openid) {
      console.log('权限错误:', {
        postOpenid: post.data._openid,
        userOpenid: openid
      });
      return {
        code: 1,
        msg: '无权更新此供应信息'
      };
    }

    // 准备更新数据
    const updateData = {
      ...event.updateData,
      updateTime: db.serverDate() // 添加更新时间
    };
    
    // 确保photoUpdated字段为日期类型
    if (event.updateData.photoUpdated) {
      updateData.photoUpdated = db.serverDate();
    }
    
    // 确保位置更新时间为日期类型
    if (event.updateData.location_updated) {
      updateData.location_updated = db.serverDate();
    }

    // 数据验证 - 修改验证逻辑，允许imageList为空数组
    if (updateData.imageList && !Array.isArray(updateData.imageList)) {
      return {
        code: 1,
        msg: '图片列表格式不正确'
      };
    }

    // 验证新图片列表格式
    if (updateData.newImageList && !Array.isArray(updateData.newImageList)) {
      return {
        code: 1,
        msg: '新图片列表格式不正确'
      };
    }

    // 验证新图片列表中每个对象的格式
    if (updateData.newImageList && updateData.newImageList.length > 0) {
      for (const imageItem of updateData.newImageList) {
        if (!imageItem.url || !imageItem.captureTime) {
          return {
            code: 1,
            msg: '新图片列表中的对象格式不正确，需要包含url和captureTime字段'
          };
        }
        // uniqueId字段是可选的，用于生成唯一文件名
        // isNewInCurrentSession字段是可选的，用于前端显示"新"标签
      }
    }

    // 验证总图片数量 - 确保至少有一种格式的图片存在
    const oldImageCount = (updateData.imageList && Array.isArray(updateData.imageList)) ? updateData.imageList.length : 0;
    const newImageCount = (updateData.newImageList && Array.isArray(updateData.newImageList)) ? updateData.newImageList.length : 0;
    const totalImageCount = oldImageCount + newImageCount;

    if (totalImageCount === 0) {
      return {
        code: 1,
        msg: '请至少上传一张图片'
      };
    }

    // 记录图片更新情况
    if (updateData.imageList) {
      console.log('更新图片列表:', {
        count: updateData.imageList.length,
        images: updateData.imageList
      });
    }

    if (updateData.newImageList) {
      console.log('更新新图片列表:', {
        count: updateData.newImageList.length,
        images: updateData.newImageList.map(item => ({
          url: item.url,
          captureTime: item.captureTime,
          uniqueId: item.uniqueId
        }))
      });
    }

    // 验证位置信息格式 - 已经不需要，因为前端直接发送GeoPoint对象
    if (updateData.location) {
      console.log('更新位置信息:', updateData.location);
    }

    console.log('准备更新数据:', {
      postId: event.id,
      updateDataKeys: Object.keys(updateData)
    });

    // 更新帖子数据
    await db.collection('supply_content')
      .doc(event.id)
      .update({
        data: updateData
      });

    // 返回成功
    return {
      code: 0,
      msg: '更新成功'
    };
  } catch (err) {
    // 提供更详细的错误信息
    console.error('更新供应信息失败:', err);
    
    let errorMsg = '更新供应信息失败';
    if (err && err.message) {
      errorMsg = err.message;
    } else if (err && err.errMsg) {
      errorMsg = err.errMsg;
    } else if (err && err.errCode) {
      errorMsg = `错误代码: ${err.errCode}`;
    }
    
    return {
      code: 1,
      msg: errorMsg,
      err: err
    };
  }
}; 