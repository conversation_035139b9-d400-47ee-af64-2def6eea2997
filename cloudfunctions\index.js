// 云函数 getSupplyListWithDistance
const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();
const _ = db.command;
const $ = db.command.aggregate;
const MAX_LIMIT = 100; // 云函数单次查询最大数量

// 计算两点之间的距离（使用Haversine公式）
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // 地球半径，单位：公里
  
  // 将角度转换为弧度
  const deg2rad = (deg) => deg * (Math.PI/180);
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  
  // Haversine公式
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // 距离，单位：公里
  
  return distance;
}

// 格式化距离显示
function formatDistance(distance) {
  if (distance < 1) {
    // 小于1公里，显示为米
    return `${Math.round(distance * 1000)}米`;
  } else if (distance < 10) {
    // 小于10公里，显示一位小数
    return `${distance.toFixed(1)}公里`;
  } else {
    // 大于等于10公里，显示整数
    return `${Math.round(distance)}公里`;
  }
}

// 验证经纬度坐标是否有效
function isValidCoordinate(lat, lon) {
  return lat !== undefined && lon !== undefined && 
         !isNaN(lat) && !isNaN(lon) && 
         lat >= -90 && lat <= 90 && 
         lon >= -180 && lon <= 180;
}

// 格式化时间为多久之前
function formatTimeAgo(dateObj) {
  if (!dateObj) return '';
  
  // 如果是字符串，转换为Date对象
  const date = typeof dateObj === 'string' ? new Date(dateObj) : dateObj;
  const now = new Date();
  
  const diff = Math.floor((now - date) / 1000); // 差异，单位：秒
  
  if (diff < 60) {
    return '刚刚';
  } else if (diff < 3600) {
    return Math.floor(diff / 60) + '分钟前';
  } else if (diff < 86400) {
    return Math.floor(diff / 3600) + '小时前';
  } else if (diff < 2592000) {
    return Math.floor(diff / 86400) + '天前';
  } else if (diff < 31536000) {
    return Math.floor(diff / 2592000) + '个月前';
  } else {
    return Math.floor(diff / 31536000) + '年前';
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const {
    page = 1,
    pageSize = 10,
    tabActive = 0,
    tabs = ['全部', '乔木/灌木', '杯苗', '藤本类', '地被类', '其他'],
    searchValue = '',
    sortType = 'latest',
    activeFilters = {},
    userLocation = null
  } = event;

  try {
    // 构建查询条件
    let query = buildQuery(tabActive, tabs, searchValue, activeFilters);
    
    // 计算跳过的数量
    const skip = (page - 1) * pageSize;
    
    // 获取总数
    const countResult = await db.collection('supply_content').where(query).count();
    const total = countResult.total;
    
    // 设置默认排序方式为创建时间降序
    let orderField = 'createTime';
    let orderDirection = 'desc';
    
    // 构建数据库查询
    let dbQuery = db.collection('supply_content').where(query);
    
    // 添加排序
    dbQuery = dbQuery.orderBy(orderField, orderDirection);
    
    // 分页
    dbQuery = dbQuery.skip(skip).limit(pageSize);
    
    // 执行查询
    const queryResult = await dbQuery.get();
    const rawData = queryResult.data;
    
    // 处理数据，格式化时间等
    const processedData = processData(rawData, userLocation);
    
    // 如果排序方式是距离最近，则对列表进行排序
    if (sortType === 'nearest' && userLocation) {
      sortByDistance(processedData, userLocation);
    }
    
    return {
      code: 0,
      data: processedData,
      total: total,
      hasMore: skip + processedData.length < total
    };
  } catch (err) {
    console.error('获取供应列表失败:', err);
    return {
      code: -1,
      msg: '获取数据失败',
      error: err
    };
  }
};

/**
 * 构建查询条件
 */
function buildQuery(tabActive, tabs, searchValue, activeFilters) {
  let query = {};
  
  // 根据分类标签筛选
  if (tabActive > 0) {
    const category = tabs[tabActive];
    query.category = category;
  }
  
  // 如果有搜索关键词
  if (searchValue) {
    // 使用正则表达式进行模糊搜索
    const keyword = searchValue;
    const keywordQuery = _.or([
      {
        title: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      },
      {
        content: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      }
    ]);
    
    // 合并分类查询和关键词查询
    if (tabActive > 0) {
      // 已有分类查询，需要同时满足分类和关键词条件
      query = _.and([
        { category: tabs[tabActive] },
        keywordQuery
      ]);
    } else {
      // 没有分类查询，只有关键词查询
      query = keywordQuery;
    }
  }
  
  // 添加筛选条件
  let filterConditions = [];
  
  // 价格筛选
  if (activeFilters.isPriceExact && activeFilters.exactPrice !== '') {
    // 精确价格筛选
    filterConditions.push({ price: Number(activeFilters.exactPrice) });
  } else {
    // 区间价格筛选
    if (activeFilters.minPrice !== undefined && activeFilters.minPrice !== '') {
      filterConditions.push({ price: _.gte(Number(activeFilters.minPrice)) });
    }
    if (activeFilters.maxPrice !== undefined && activeFilters.maxPrice !== '') {
      filterConditions.push({ price: _.lte(Number(activeFilters.maxPrice)) });
    }
  }
  
  // 高度筛选
  if (activeFilters.isHeightExact && activeFilters.exactHeight !== '') {
    // 精确高度筛选
    filterConditions.push({ height: Number(activeFilters.exactHeight) });
  } else {
    // 区间高度筛选
    if (activeFilters.minHeight !== undefined && activeFilters.minHeight !== '') {
      filterConditions.push({ height: _.gte(Number(activeFilters.minHeight)) });
    }
    if (activeFilters.maxHeight !== undefined && activeFilters.maxHeight !== '') {
      filterConditions.push({ height: _.lte(Number(activeFilters.maxHeight)) });
    }
  }
  
  // 冠幅筛选
  if (activeFilters.isCanopyExact && activeFilters.exactCanopy !== '') {
    // 精确冠幅筛选
    filterConditions.push({ canopy: Number(activeFilters.exactCanopy) });
  } else {
    // 区间冠幅筛选
    if (activeFilters.minCanopy !== undefined && activeFilters.minCanopy !== '') {
      filterConditions.push({ canopy: _.gte(Number(activeFilters.minCanopy)) });
    }
    if (activeFilters.maxCanopy !== undefined && activeFilters.maxCanopy !== '') {
      filterConditions.push({ canopy: _.lte(Number(activeFilters.maxCanopy)) });
    }
  }
  
  // 米径筛选
  if (activeFilters.isMeterDiameterExact && activeFilters.exactMeterDiameter !== '') {
    // 精确米径筛选
    filterConditions.push({ meter_diameter: Number(activeFilters.exactMeterDiameter) });
  } else {
    // 区间米径筛选
    if (activeFilters.minMeterDiameter !== undefined && activeFilters.minMeterDiameter !== '') {
      filterConditions.push({ meter_diameter: _.gte(Number(activeFilters.minMeterDiameter)) });
    }
    if (activeFilters.maxMeterDiameter !== undefined && activeFilters.maxMeterDiameter !== '') {
      filterConditions.push({ meter_diameter: _.lte(Number(activeFilters.maxMeterDiameter)) });
    }
  }
  
  // 杯口筛选
  if (activeFilters.isCupExact && activeFilters.exactCup !== '') {
    // 精确杯口筛选
    filterConditions.push({ cup: Number(activeFilters.exactCup) });
  } else {
    // 区间杯口筛选
    if (activeFilters.minCup !== undefined && activeFilters.minCup !== '') {
      filterConditions.push({ cup: _.gte(Number(activeFilters.minCup)) });
    }
    if (activeFilters.maxCup !== undefined && activeFilters.maxCup !== '') {
      filterConditions.push({ cup: _.lte(Number(activeFilters.maxCup)) });
    }
  }
  
  // 胸径筛选
  if (activeFilters.isChestDiameterExact && activeFilters.exactChestDiameter !== '') {
    // 精确胸径筛选
    filterConditions.push({ thorax_diameter: Number(activeFilters.exactChestDiameter) });
  } else {
    // 区间胸径筛选
    if (activeFilters.minChestDiameter !== undefined && activeFilters.minChestDiameter !== '') {
      filterConditions.push({ thorax_diameter: _.gte(Number(activeFilters.minChestDiameter)) });
    }
    if (activeFilters.maxChestDiameter !== undefined && activeFilters.maxChestDiameter !== '') {
      filterConditions.push({ thorax_diameter: _.lte(Number(activeFilters.maxChestDiameter)) });
    }
  }
  
  // 地径筛选
  if (activeFilters.isGroundDiameterExact && activeFilters.exactGroundDiameter !== '') {
    // 精确地径筛选
    filterConditions.push({ ground_diameter: Number(activeFilters.exactGroundDiameter) });
  } else {
    // 区间地径筛选
    if (activeFilters.minGroundDiameter !== undefined && activeFilters.minGroundDiameter !== '') {
      filterConditions.push({ ground_diameter: _.gte(Number(activeFilters.minGroundDiameter)) });
    }
    if (activeFilters.maxGroundDiameter !== undefined && activeFilters.maxGroundDiameter !== '') {
      filterConditions.push({ ground_diameter: _.lte(Number(activeFilters.maxGroundDiameter)) });
    }
  }
  
  // 分支点筛选
  if (activeFilters.isBranchPosExact && activeFilters.exactBranchPos !== '') {
    // 精确分支点筛选
    filterConditions.push({ branchPos: Number(activeFilters.exactBranchPos) });
  } else {
    // 区间分支点筛选
    if (activeFilters.minBranchPos !== undefined && activeFilters.minBranchPos !== '') {
      filterConditions.push({ branchPos: _.gte(Number(activeFilters.minBranchPos)) });
    }
    if (activeFilters.maxBranchPos !== undefined && activeFilters.maxBranchPos !== '') {
      filterConditions.push({ branchPos: _.lte(Number(activeFilters.maxBranchPos)) });
    }
  }
  
  // 丛生数量筛选
  if (activeFilters.isClumpCountExact && activeFilters.exactClumpCount !== '') {
    // 精确丛生数量筛选
    filterConditions.push({ clumpCount: Number(activeFilters.exactClumpCount) });
  } else {
    // 区间丛生数量筛选
    if (activeFilters.minClumpCount !== undefined && activeFilters.minClumpCount !== '') {
      filterConditions.push({ clumpCount: _.gte(Number(activeFilters.minClumpCount)) });
    }
    if (activeFilters.maxClumpCount !== undefined && activeFilters.maxClumpCount !== '') {
      filterConditions.push({ clumpCount: _.lte(Number(activeFilters.maxClumpCount)) });
    }
  }
  
  // 杆径筛选
  if (activeFilters.isClumpDiameterExact && activeFilters.exactClumpDiameter !== '') {
    // 精确杆径筛选
    filterConditions.push({ clumpDiameter: Number(activeFilters.exactClumpDiameter) });
  } else {
    // 区间杆径筛选
    if (activeFilters.minClumpDiameter !== undefined && activeFilters.minClumpDiameter !== '') {
      filterConditions.push({ clumpDiameter: _.gte(Number(activeFilters.minClumpDiameter)) });
    }
    if (activeFilters.maxClumpDiameter !== undefined && activeFilters.maxClumpDiameter !== '') {
      filterConditions.push({ clumpDiameter: _.lte(Number(activeFilters.maxClumpDiameter)) });
    }
  }
  
  // 质量筛选
  if (activeFilters.quality !== undefined && activeFilters.quality !== 'all') {
    filterConditions.push({ quality: activeFilters.quality });
  }
  
  // 合并筛选条件
  if (filterConditions.length > 0) {
    if (Object.keys(query).length > 0) {
      // 已有查询条件，需要同时满足
      if (query.hasOwnProperty('$and')) {
        // 已经是 and 条件，添加筛选条件
        query.$and = [...query.$and, ...filterConditions];
      } else {
        // 转换为 and 条件
        query = _.and([query, ...filterConditions]);
      }
    } else {
      // 没有查询条件，直接使用筛选条件
      if (filterConditions.length === 1) {
        query = filterConditions[0];
      } else {
        query = _.and(filterConditions);
      }
    }
  }
  
  // 如果没有任何查询条件，添加时间限制避免全表扫描
  if (Object.keys(query).length === 0) {
    // 添加最近180天的时间限制
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setDate(sixMonthsAgo.getDate() - 180);
    query.createTime = _.gte(sixMonthsAgo);
  }
  
  return query;
}

/**
 * 计算用户与供应信息发布位置的距离
 */
function calculateDistanceFromUserLocation(userLocation, itemLocation) {
  if (!userLocation || !itemLocation) {
    return null;
  }
  
  let lat1, lon1, lat2, lon2;
  let hasValidLocation = false;
  
  // 处理用户位置
  if (userLocation.coordinates && Array.isArray(userLocation.coordinates) && userLocation.coordinates.length >= 2) {
    lon1 = userLocation.coordinates[0];
    lat1 = userLocation.coordinates[1];
    hasValidLocation = true;
  } else if (userLocation.longitude !== undefined && userLocation.latitude !== undefined) {
    lon1 = userLocation.longitude;
    lat1 = userLocation.latitude;
    hasValidLocation = true;
  }
  
  // 处理供应位置
  if (itemLocation.coordinates && Array.isArray(itemLocation.coordinates) && itemLocation.coordinates.length >= 2) {
    lon2 = itemLocation.coordinates[0];
    lat2 = itemLocation.coordinates[1];
    hasValidLocation = hasValidLocation && true;
  } else if (itemLocation.longitude !== undefined && itemLocation.latitude !== undefined) {
    lon2 = itemLocation.longitude;
    lat2 = itemLocation.latitude;
    hasValidLocation = hasValidLocation && true;
  }
  
  // 检查是否有有效的位置信息
  if (!hasValidLocation) {
    return null;
  }
  
  // 检查坐标是否有效
  if (!isValidCoordinate(lat1, lon1)) {
    return null;
  }
  
  if (!isValidCoordinate(lat2, lon2)) {
    return null;
  }
  
  // 计算距离
  const distance = calculateDistance(lat1, lon1, lat2, lon2);
  return {
    distance: distance,
    lat1: lat1,
    lon1: lon1,
    lat2: lat2,
    lon2: lon2
  };
}

/**
 * 处理原始数据，添加时间和距离信息
 */
function processData(rawData, userLocation) {
  return rawData.map(item => {
    const originalTitle = item.title || '';
    
    // 计算时间
    const timeAgo = formatTimeAgo(item.createTime);
    
    // 计算距离
    let calculatedDistance = null;
    let distanceValue = null;
    
    if (userLocation && item.location) {
      const distanceInfo = calculateDistanceFromUserLocation(userLocation, item.location);
      if (distanceInfo) {
        calculatedDistance = "距离你" + formatDistance(distanceInfo.distance);
        distanceValue = distanceInfo.distance;
      }
    }
    
    return {
      id: item._id,
      titlePrefix: '【供应】',
      titleContent: originalTitle,
      title: `【供应】${originalTitle}`,
      content: item.content || '',
      price: item.price || 0,
      category: item.category || '其他',
      images: item.imageList || [],
      publishTime: item.createTime,
      timeAgo: timeAgo,
      location: item.location,
      calculatedDistance: calculatedDistance,
      distanceValue: distanceValue, // 添加距离值用于排序
      plant_method: item.plant_method || '', // 添加栽培状态字段
      clumpCount: item.clumpCount, // 添加丛生数量
      clumpDiameter: item.clumpDiameter, // 添加杆径
      rawData: item // 保存原始数据以便后续使用
    };
  });
}

/**
 * 按距离排序
 */
function sortByDistance(dataList, userLocation) {
  if (!userLocation || !dataList || dataList.length === 0) return;
  
  dataList.sort((a, b) => {
    // 使用预先计算的距离值进行排序
    const distanceA = a.distanceValue;
    const distanceB = b.distanceValue;
    
    // 无效距离排在后面
    if (distanceA === null && distanceB === null) return 0;
    if (distanceA === null) return 1;
    if (distanceB === null) return -1;
    
    // 按距离排序
    return distanceA - distanceB;
  });
} 