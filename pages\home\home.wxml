<!--pages/home/<USER>
<page-meta>
  <navigation-bar title="首页" back="{{false}}" extClass="custom-nav"></navigation-bar>
</page-meta>



<!-- 欢迎页面组件 -->
<welcome-screen visible="{{showWelcomeScreen}}" bind:close="onWelcomeClose"></welcome-screen>

<!-- 树叶飘落动画效果 -->
<view class="leaves-container" wx:if="{{leavesVisible}}" style="z-index: {{leavesZIndex}};">
  <view 
    class="leaf" 
    wx:for="{{leaves}}" 
    wx:key="id" 
    style="--fall-delay: {{item.delay}}s; --fall-duration: {{item.duration}}s; --leaf-size: {{item.size}}rpx; --start-pos: {{item.startPos}}%;"
    bind:animationend="onLeafAnimationEnd"
    data-leaf-id="{{item.id}}">
    <image 
      class="leaf-image" 
      src="{{item.type === 1 ? '/images/leaf1.png' : '/images/leaf2.png'}}"
      mode="aspectFit"
      style="transform: rotate({{item.id % 2 === 0 ? '' : '-'}}{{Math.floor(item.id * 30) % 360}}deg);"
    ></image>
  </view>
</view>


<!-- 页面内容区 -->
<view class="home-content" bindscroll="onPageScroll">
  <!-- 交易中心功能区 -->
  <view class="trading-center" style="width: 712rpx; display: block; box-sizing: content-box; left: 0rpx; top: 0rpx">
  
    <view class="section-header">
      <image class="header-icon-left" src="/images/checklist.png" mode="aspectFit"></image>
      <view class="header-decoration"></view>
      <text class="header-title">成都苗木中心</text>
      <view class="header-decoration"></view>
      <image class="header-icon-right" src="/images/titlePlant.png" mode="aspectFit"></image>
    </view>
    
    <!-- 供应中心和求购中心卡片（并排显示） -->
    <view class="trading-cards-row">
      <!-- 供应中心卡片 -->
      <view class="card-wrapper card-half">
        <view class="trading-card supply-card" bindtap="onSupplyTap" hover-class="card-hover" >
          <view class="card-dots-decoration"></view>
          <view class="card-line-decoration top-right"></view>
          <view class="card-line-decoration bottom-left"></view>
          <view class="card-top">
            <view class="card-icon-wrapper">
              <image class="card-icon" src="/images/supply.png" mode="aspectFit"></image>
            </view>
            <view class="card-title-wrapper">
              <text class="card-title"><text class="highlight-char">供</text>应中心</text>
            </view>
          </view>
          <view class="card-divider"></view>
          <view class="card-description">
            <text class="first-char">点</text>这里发布您的优质绿植产品，让更多买家发现
          </view>
          <view class="card-slogan">
            <text class="slogan-text">发现优质商品的绿色平台</text>
            <view class="card-slogan-image">
              <image src="/images/art_font1.png" mode="widthFix"></image>
            </view>
          </view>
        </view>
        <!-- 按钮放在卡片外部 -->
        <view class="card-badge supply-badge" bindtap="onCreateSupplyTap" hover-stop-propagation="true" hover-class="badge-hover">发起供应</view>
      </view>
      
      <!-- 求购中心卡片 -->
      <view class="card-wrapper card-half">
        <view class="trading-card demand-card" bindtap="onDemandTap" hover-class="card-hover">
          <view class="card-dots-decoration"></view>
          <view class="card-line-decoration top-left"></view>
          <view class="card-line-decoration bottom-right"></view>
          <view class="card-top">
            <view class="card-icon-wrapper">
              <image class="card-icon" src="/images/demand.png" mode="aspectFit"></image>
            </view>
            <view class="card-title-wrapper">
              <text class="card-title"><text class="highlight-char">求</text>购中心</text>
            </view>
          </view>
          <view class="card-divider"></view>
          <view class="card-description">
            <text class="first-char">点</text>这里发布您的求购需求，快速匹配合适卖家
          </view>
          <view class="card-slogan">
            <text class="slogan-text">找到理想买家的绿色渠道</text>
            <view class="card-slogan-image">
              <image src="/images/art_font2.png" mode="widthFix"></image>
            </view>
          </view>
        </view>
        <!-- 按钮放在卡片外部 -->
        <view class="card-badge demand-badge" bindtap="onCreateDemandTap" hover-stop-propagation="true" hover-class="badge-hover">发布需求</view>
      </view>
    </view>
  
    <!-- 搜索栏放到供应卡片和通知栏之间 -->
    <view class="search-bar-wrapper" style="margin-left:-30rpx; width:calc(100% + 60rpx);">
      <view class="search-bar">
        <view class="search-input-container" bindtap="navigateToSupply">
          <view class="search-icon-wrapper">
            <image class="search-icon" src="https://6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888.tcb.qcloud.la/static/search.png?sign=45f5b30e951d2e64f63da9926b2ca89d&t=1750401158" mode="aspectFit"></image>
          </view>
          <view class="search-placeholder">搜索苗木、花卉、园艺资材...</view>
        </view>
        
      </view>
    </view>
  

  
    <!-- 用户发布通知走马灯容器 - 白色背景 -->
    <view class="notice-full-width-container">
      <view class="notice-container-white" style="position: relative; left: -30rpx; top: 0rpx">
        <view class="notice-header">
          <view class="notice-title-icon"></view>
          <text class="notice-section-title">最新通知</text>
        </view>
        
        <!-- 当通知列表为空时显示空状态 -->
        <view class="notice-empty-container" wx:if="{{!noticeList || noticeList.length === 0}}">
          <t-empty icon="component-space" description="暂时没有通知" />
        </view>
        
        <!-- 当有通知时显示通知列表 -->
        <scroll-view 
          class="notice-marquee-wrapper-full" 
          scroll-y="true" 
          scroll-top="{{noticeScrollTop}}"
          bindtouchstart="onNoticeTouchStart"
          bindtouchend="onNoticeTouchEnd"
          bindscroll="onNoticeScroll"
          enhanced="true"
          show-scrollbar="false"
          wx:else>
          <view class="notice-marquee">
            <view 
              class="notice-item {{activeNoticeId === item.id ? 'notice-item-active' : ''}}" 
              wx:for="{{noticeList}}" 
              wx:key="id" 
              catch:tap="onNoticeItemTap" 
              data-id="{{item.id}}">
              <view class="notice-content">
                <text class="notice-username">{{item.publisherNickname || '用户'}}</text>
                <text class="notice-text">发布了</text>
                <text class="notice-product-info" wx:if="{{item.titleText}}">{{item.titleText}}</text>
                <text class="notice-text">的{{item.postType === 'supply' ? '供应' : '求购'}}信息</text>
                <text class="notice-time">({{item.timeText}})</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    
    <!-- 使用教程卡片 -->
    <view class="tutorial-card">
      <view class="tutorial-icon"></view>
      <text class="tutorial-text">通知栏内部信息可以点击跳转查看详细页面</text>
    </view>
    
    <!-- 新增服务卡片容器 -->
    <view class="section-header">
      <image class="header-icon-left" src="/images/checklist.png" mode="aspectFit"></image>
      <view class="header-decoration"></view>
      <text class="header-title">增值服务</text>
      <view class="header-decoration"></view>
      <image class="header-icon-right" src="/images/titlePlant.png" mode="aspectFit"></image>
    </view>
    
    <!-- 新增服务卡片布局 -->
    <view class="service-cards-container">
      <!-- 第一行：左侧大卡片和右侧两小卡片 -->
      <view class="service-cards-row">
        <!-- 左侧大卡片 - 退林还耕 -->
        <view class="service-card-large">
          <view class="service-card forest-return-card" bindtap="onForestReturnTap" hover-class="card-hover" style="position: relative; left: 2rpx; top: 0rpx">
            <view class="service-card-content">
              <view class="service-card-title"><text class="highlight-char">退</text>林还耕</view>
              <view class="service-card-desc">快速<text class="highlight-text">处理</text>、退林还耕、<text class="highlight-text">土地改造</text></view>
              <view class="service-card-slogan">助力农业可持续发展</view>
            </view>
            <view class="service-card-icon-wrapper">
              <image class="service-card-icon" src="/images/sprout.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>

        <!-- 右侧两卡片布局 -->
        <view class="service-cards-right">
          <!-- 右上卡片 - 苗圃转让 -->
          <view class="service-card-small">
            <view class="service-card nursery-card" bindtap="onNurseryTap" hover-class="card-hover">
              <view class="service-card-content">
                <view class="service-card-title"><text class="highlight-char">苗</text>圃转让</view>
                <view class="service-card-desc"><text class="highlight-text">转让经营</text>，超低价格</view>
              </view>
              <view class="service-card-icon-wrapper small">
                <image class="service-card-icon" src="/images/organic.png" mode="aspectFit"></image>
              </view>
            </view>
          </view>


          <!-- 右下卡片 - 报价系统 -->
          <view class="service-card-small">
            <view class="service-card design-card" bindtap="onDesignTap" hover-class="card-hover">
              <view class="service-card-content">
                <view class="service-card-title"><text class="highlight-char">工</text>程报价</view>
                <view class="service-card-desc"><text class="highlight-text">快速</text>计价、<text class="highlight-text">精准报价</text></view>
              </view>
              <view class="service-card-icon-wrapper small">
                <image class="service-card-icon" src="/images/planting.png" mode="aspectFit"></image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 第二行：视频教程卡片 - 独占一行 -->
      <view class="service-card-full-width">
        <!-- 气泡动画容器 -->
        <view class="bubbles-container" wx:if="{{bubblesVisible}}">
          <view
            class="bubble"
            wx:for="{{bubbles}}"
            wx:key="id"
            style="--bubble-delay: {{item.delay}}s; --bubble-duration: {{item.duration}}s; --bubble-size: {{item.size}}rpx; --start-pos: {{item.startPos}}%; --bubble-opacity: {{item.opacity}};"
            bind:animationend="onBubbleAnimationEnd"
            data-bubble-id="{{item.id}}">
          </view>
        </view>

        <view class="service-card video-tutorial-card" bindtap="onVideoTutorialTap" hover-class="card-hover" style="position: relative; left: 0rpx; top: -4rpx">
          <view class="service-card-content">
            <view class="service-card-title" style="position: relative; left: 0rpx; top:0rpx"><text class="highlight-char">视</text>频教程</view>
            <view class="service-card-desc"><text class="highlight-text">视频</text>指导、<text class="highlight-text">了解技巧</text>、助您快速上手</view>
            <view class="service-card-slogan">发布求购和供应以及其它教程</view>
          </view>
          <view class="service-card-icon-wrapper">
            <t-icon name="video-library" size="80rpx" color="rgba(255,255,255,0.9)"></t-icon>
          </view>
        </view>
      </view>
    </view>
    
 <!-- 平台统计数据区域 -->
 <view class="app-stats-footer">
      <view class="stats-header">
        <view class="stats-header-line"></view>
        <text class="stats-header-text">平台统计</text>
        <view class="stats-header-line"></view>
      </view>
      
      <view class="stats-container">
        <view class="stats-row">
          <!-- 统计项目 - 发布信息 -->
          <view class="stats-item">
            <view class="stats-icon posts-icon"></view>
            <view class="stats-data">
              <text class="stats-number">{{statsData.postsCount || '2,568'}}</text>
              <text class="stats-label">发布信息</text>
            </view>
          </view>
          
          <!-- 统计项目 - 登录次数 -->
          <view class="stats-item">
            <view class="stats-icon logins-icon"></view>
            <view class="stats-data">
              <text class="stats-number">{{statsData.loginsCount || '15,342'}}</text>
              <text class="stats-label">登录次数</text>
            </view>
          </view>
        
          <!-- 统计项目 - 用户总量 -->
          <view class="stats-item">
            <view class="stats-icon users-icon"></view>
            <view class="stats-data">
              <text class="stats-number">{{statsData.usersCount || '872'}}</text>
              <text class="stats-label">用户总量</text>
            </view>
          </view>
          
          <!-- 统计项目 - 在线信息 -->
          <view class="stats-item">
            <view class="stats-icon active-icon"></view>
            <view class="stats-data">
              <text class="stats-number">{{statsData.activePostsCount || '1,245'}}</text>
              <text class="stats-label">浏览统计</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="footer-copyright">
        
        <text class="icp-text">蜀ICP备2025145021号-1X</text>
        <image class="footer-park-image" src="/images/park.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 底部安全区域，防止内容被tabbar遮挡 -->
    <view class="bottom-safe-area"></view>
  </view>
</view>

    

<!-- 下载提示组件 -->
<download-tip bind:confirm="onDownloadConfirm" visible="{{showDownloadTip}}"></download-tip>

<!-- 返回顶部组件 -->
<t-back-top theme="round" text="顶部" bind:to-top="onBackToTop" custom-class="custom-back-top" wx:if="{{showBackTop}}"></t-back-top>

<!-- 客服悬浮按钮 -->
<view class="customer-service-btn" bindtap="callCustomerService">
  <t-icon name="call-1" size="40rpx" color="#ffffff"></t-icon>
  <text class="customer-service-text">客服</text>
</view>

<!-- 加载动画 -->
<view class="loading-container" wx:if="{{loading}}">
  <t-loading theme="circular" size="60rpx" text="加载中..." inheritColor></t-loading>
</view>

