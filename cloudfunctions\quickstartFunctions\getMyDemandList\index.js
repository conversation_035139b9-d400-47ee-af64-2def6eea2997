// 云函数 getMyDemandList   获取我的求购页面
// 排序逻辑：优先显示有新回价的求购(hasNewReply=true)，然后按创建时间降序排列
const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();
const _ = db.command;
const MAX_LIMIT = 100; // 云函数单次查询最大数量

// 格式化时间为标准日期格式
function formatDate(dateObj) {
  if (!dateObj) return '';
  
  // 如果是字符串，转换为Date对象
  const date = typeof dateObj === 'string' ? new Date(dateObj) : dateObj;
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID; // 获取用户的openid
  
  if (!openid) {
    return {
      code: -2,
      msg: '未获取到用户身份',
      data: [],
      total: 0,
      hasMore: false
    };
  }
  
  const {
    page = 1,
    pageSize = 10,
    searchKeyword = ''
  } = event;
  
  try {
    // 构建查询条件
    let query = {
      _openid: openid // 只查询当前用户的求购信息
    };
    
    // 如果有搜索关键词，添加标题搜索条件
    if (searchKeyword) {
      query.title = db.RegExp({
        regexp: searchKeyword,
        options: 'i' // 不区分大小写
      });
    }
    
    // 计算跳过的数量
    const skip = (page - 1) * pageSize;
    
    // 获取总数
    const countResult = await db.collection('demand_content').where(query).count();
    const total = countResult.total;
    
    // 构建数据库查询 - 混合排序：优先显示有新回价的，然后按创建时间降序
    let dbQuery = db.collection('demand_content')
      .where(query)
      .orderBy('hasNewReply', 'desc') // 第一优先级：有新回价的排在前面
      .orderBy('createTime', 'desc')  // 第二优先级：按创建时间降序排列
      .skip(skip)
      .limit(pageSize);
    
    // 执行查询
    const queryResult = await dbQuery.get();
    const rawData = queryResult.data;
    
    // 处理数据，格式化时间和价格
    const processedData = rawData.map(item => {
      return {
        ...item,
        formattedTime: formatDate(item.createTime),
        formattedPrice: item.price ? `¥${item.price}` : '电话议价'
      };
    });
    
    return {
      code: 0,
      data: processedData,
      total: total,
      hasMore: skip + processedData.length < total
    };
  } catch (err) {
    return {
      code: -1,
      msg: '获取数据失败',
      error: err,
      data: [],
      total: 0,
      hasMore: false
    };
  }
}; 