// 云函数：获取用户所有求购的新回价总数（聚合查询优化版）
// 解决分页查询导致的红点丢失问题
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const $ = db.command.aggregate;

/**
 * 获取用户所有求购的新回价总数
 * 使用聚合查询确保统计所有记录，解决分页限制问题
 * 
 * @param {Object} event - 云函数事件参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 统计结果
 */
exports.main = async (event, context) => {
  try {
    // 获取用户openid
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;

    if (!openid) {
      return {
        code: -2,
        msg: '未获取到用户身份',
        totalNewReplies: 0,
        hasNewReplyCount: 0,
        hasNewReplies: false
      };
    }

    console.log('开始统计用户新回价数量，openid:', openid);

    // 先查询用户总的求购数量，用于对比和调试
    const totalDemandCount = await db.collection('demand_content')
      .where({ _openid: openid })
      .count();

    console.log('用户总求购数量:', totalDemandCount.total);

    // 使用聚合查询统计所有新回价数量
    // 这种方式可以统计所有记录，不受分页限制
    const aggregateResult = await db.collection('demand_content')
      .aggregate()
      .match({
        _openid: openid,
        newReplyCount: $.gt(0) // 只统计有新回价的记录
      })
      .group({
        _id: null,
        totalNewReplies: $.sum('$newReplyCount'), // 累加所有新回价数量
        hasNewReplyCount: $.sum(1) // 统计有新回价的帖子数量
      })
      .end();

    console.log('聚合查询结果:', {
      list: aggregateResult.list,
      total: aggregateResult.list.length,
      userTotalDemands: totalDemandCount.total
    });

    // 处理查询结果
    const result = aggregateResult.list[0] || { 
      totalNewReplies: 0, 
      hasNewReplyCount: 0 
    };

    const response = {
      code: 0,
      msg: '获取新回价统计成功',
      totalNewReplies: result.totalNewReplies || 0,
      hasNewReplyCount: result.hasNewReplyCount || 0,
      hasNewReplies: (result.totalNewReplies || 0) > 0,
      // 调试信息
      debug: {
        userTotalDemands: totalDemandCount.total,
        aggregateResultCount: aggregateResult.list.length
      }
    };

    console.log('返回结果:', response);
    return response;

  } catch (error) {
    console.error('获取新回价统计失败:', error);
    return {
      code: -1,
      msg: '获取新回价统计失败: ' + error.message,
      totalNewReplies: 0,
      hasNewReplyCount: 0,
      hasNewReplies: false,
      error: error.message
    };
  }
};
