// 云函数 - getNurseryDetail   渲染苗圃详情
// 获取苗圃转让信息详情

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 主函数
exports.main = async (event, context) => {
  try {
    // 检查参数
    if (!event.id) {
      return {
        code: 1,
        msg: '缺少必要参数id'
      };
    }

    // 获取苗圃详情
    const result = await db.collection('nursery_content')
      .doc(event.id)
      .get();

    // 如果找不到苗圃信息
    if (!result || !result.data) {
      return {
        code: 1,
        msg: '找不到对应的苗圃转让信息'
      };
    }

    // 返回苗圃数据
    return {
      code: 0,
      data: result.data
    };
  } catch (err) {
    console.error('获取苗圃详情失败:', err);
    return {
      code: 1,
      msg: '获取苗圃详情失败',
      err: err
    };
  }
}; 