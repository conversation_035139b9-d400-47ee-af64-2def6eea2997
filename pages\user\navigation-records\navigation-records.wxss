.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 0;
}

/* 导航栏容器 */
.nav-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #fff;
}

/* 导航栏占位元素 */
.nav-placeholder {
  width: 100%;
  background: #43a047;
  position: relative;
}

/* 自定义导航栏样式 */
.custom-nav {
  background: #43a047 !important;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 999 !important;
  position: relative !important;
  width: 100% !important;
}

.custom-nav::after {
  display: none;
}

.custom-nav .weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.custom-nav .weui-navigation-bar__center {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  font-weight: bold;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper {
  transition: all 0.3s ease;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper.weui-active {
  opacity: 0.7;
  transform: scale(0.95);
}

/* 主内容区 */
.content-container {
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

/* TDesign图标样式 */
.empty-container t-icon {
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 记录列表 */
.records-list {
  padding: 0;
}

.record-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.record-content {
  padding: 30rpx;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.post-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
  line-height: 1.4;
}

.nav-time {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.record-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.address {
  font-size: 28rpx;
  color: #4caf50;
  font-weight: 500;
  flex: 1;
  margin-right: 20rpx;
}

.post-type-tag {
  background-color: #e8f5e8;
  color: #2e7d32;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  white-space: nowrap;
}

.record-footer {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 15rpx;
}

.nav-time-full {
  font-size: 24rpx;
  color: #999;
}

/* 操作按钮 */
.record-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: background-color 0.2s;
}

.navigate-btn {
  color: #4caf50;
  border-right: 1rpx solid #f0f0f0;
}

.navigate-btn:active {
  background-color: #f5f5f5;
}

.delete-btn {
  color: #f44336;
}

.delete-btn:active {
  background-color: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .record-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .nav-time {
    margin-top: 10rpx;
  }
  
  .record-body {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .post-type-tag {
    margin-top: 10rpx;
  }
}
