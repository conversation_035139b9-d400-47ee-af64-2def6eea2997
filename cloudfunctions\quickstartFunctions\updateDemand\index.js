// 云函数 - updateDemand
// 更新求购信息

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 主函数
exports.main = async (event, context) => {
  try {
    // 检查参数
    if (!event.id) {
      return {
        code: 1,
        msg: '缺少必要参数id'
      };
    }

    if (!event.updateData || Object.keys(event.updateData).length === 0) {
      return {
        code: 1,
        msg: '缺少更新数据'
      };
    }

    // 获取用户信息
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;

    if (!openid) {
      return {
        code: 1,
        msg: '获取用户身份失败'
      };
    }

    console.log('求购更新请求数据:', { 
      postId: event.id, 
      openid: openid,
      updateDataKeys: Object.keys(event.updateData)
    });

    // 先检查帖子是否存在且属于当前用户
    const post = await db.collection('demand_content')
      .doc(event.id)
      .get();

    if (!post || !post.data) {
      return {
        code: 1,
        msg: '找不到对应的求购信息'
      };
    }

    // 检查帖子是否属于当前用户
    if (post.data._openid !== openid) {
      console.log('权限错误:', {
        postOpenid: post.data._openid,
        userOpenid: openid
      });
      return {
        code: 1,
        msg: '无权更新此求购信息'
      };
    }

    // 准备更新数据
    const updateData = {
      ...event.updateData,
      updateTime: db.serverDate() // 添加更新时间
    };
    
    // 保留原始图片列表
    updateData.imageList = post.data.imageList || [];

    console.log('准备更新求购数据:', {
      postId: event.id,
      updateDataKeys: Object.keys(updateData)
    });

    // 更新帖子数据
    await db.collection('demand_content')
      .doc(event.id)
      .update({
        data: updateData
      });

    // 返回成功
    return {
      code: 0,
      msg: '更新成功'
    };
  } catch (err) {
    // 提供更详细的错误信息
    console.error('更新求购信息失败:', err);
    
    let errorMsg = '更新求购信息失败';
    if (err && err.message) {
      errorMsg = err.message;
    } else if (err && err.errMsg) {
      errorMsg = err.errMsg;
    } else if (err && err.errCode) {
      errorMsg = `错误代码: ${err.errCode}`;
    }
    
    return {
      code: 1,
      msg: errorMsg,
      err: err
    };
  }
}; 