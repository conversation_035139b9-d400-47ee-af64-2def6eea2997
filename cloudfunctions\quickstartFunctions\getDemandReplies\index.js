const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 格式化时间为多久之前
function formatTimeAgo(dateObj) {
  if (!dateObj) return '';
  
  const date = typeof dateObj === 'string' ? new Date(dateObj) : dateObj;
  const now = new Date();
  
  const diff = Math.floor((now - date) / 1000);
  
  if (diff < 60) {
    return '刚刚';
  } else if (diff < 3600) {
    return Math.floor(diff / 60) + '分钟前';
  } else if (diff < 86400) {
    return Math.floor(diff / 3600) + '小时前';
  } else if (diff < 2592000) {
    return Math.floor(diff / 86400) + '天前';
  } else if (diff < 31536000) {
    return Math.floor(diff / 2592000) + '个月前';
  } else {
    return Math.floor(diff / 31536000) + '年前';
  }
}

exports.main = async (event, context) => {
  const { demandPostId } = event;
  
  if (!demandPostId) {
    return {
      code: 1,
      msg: '缺少求购帖子ID'
    };
  }
  
  try {
    // 查询该求购帖子的所有回价记录
    const replyResult = await db.collection('demand_reply')
      .where({
        demand_PostId: demandPostId,
        status: 'active' // 只查询有效的回价
      })
      .orderBy('createTime', 'desc') // 按创建时间降序排列
      .get();
    
    console.log(`求购 ${demandPostId} 找到回价记录数量: ${replyResult.data.length}`);
    
    // 处理回价数据
    const processedReplies = replyResult.data.map(reply => {
      return {
        id: reply._id,
        price: reply.price,
        quantity: reply.quantity,
        unit: reply.unit || '株',
        address: reply.address || '地址未提供',
        content: reply.content || '',
        imageList: reply.imageList || [],
        firstImage: reply.imageList && reply.imageList.length > 0 ? reply.imageList[0] : '',
        replierOpenid: reply.replierOpenid,
        replierPhoneNumber: reply.replierPhoneNumber || '',
        createTime: reply.createTime,
        timeAgo: formatTimeAgo(reply.createTime),
        location: reply.location
      };
    });
    
    return {
      code: 0,
      data: processedReplies,
      total: processedReplies.length,
      msg: '获取回价列表成功'
    };
    
  } catch (error) {
    console.error('获取回价列表失败:', error);
    return {
      code: 1,
      msg: '获取回价列表失败: ' + error.message,
      data: [],
      total: 0
    };
  }
};
